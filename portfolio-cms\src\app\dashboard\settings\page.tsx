'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Database, 
  Upload, 
  BarChart3, 
  User, 
  Key, 
  Globe,
  CheckCircle,
  AlertCircle 
} from 'lucide-react'

export default function SettingsPage() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(false)

  const configSections = [
    {
      title: 'Database Configuration',
      description: 'PostgreSQL database connection',
      icon: Database,
      status: 'connected',
      items: [
        { label: 'Database URL', value: 'postgresql://***@neon.tech/***', masked: true },
        { label: 'Connection Pool', value: 'Active', status: 'success' },
        { label: 'Last Migration', value: '2025-07-01 07:42:52', status: 'success' },
      ]
    },
    {
      title: 'Cloudinary Integration',
      description: 'Image upload and management service',
      icon: Upload,
      status: 'configured',
      items: [
        { label: 'Cloud Name', value: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || 'Not configured' },
        { label: 'Upload Preset', value: 'portfolio', status: 'success' },
        { label: 'Storage Used', value: '0 MB / 25 GB', status: 'success' },
      ]
    },
    {
      title: 'Google Analytics',
      description: 'Website analytics and tracking',
      icon: BarChart3,
      status: 'pending',
      items: [
        { label: 'Property ID', value: 'Not configured', status: 'warning' },
        { label: 'Tracking Status', value: 'Inactive', status: 'warning' },
        { label: 'Data Collection', value: 'Disabled', status: 'warning' },
      ]
    },
    {
      title: 'Authentication',
      description: 'User authentication and security',
      icon: Key,
      status: 'active',
      items: [
        { label: 'NextAuth Secret', value: 'Configured', status: 'success' },
        { label: 'Session Strategy', value: 'JWT', status: 'success' },
        { label: 'Active Sessions', value: '1', status: 'success' },
      ]
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
      case 'connected':
      case 'active':
      case 'configured':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      default:
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
      case 'active':
      case 'configured':
        return <Badge variant="default" className="bg-green-500">Connected</Badge>
      case 'pending':
        return <Badge variant="secondary">Pending Setup</Badge>
      default:
        return <Badge variant="destructive">Disconnected</Badge>
    }
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your CMS configuration and integrations
          </p>
        </div>

        {/* User Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              User Information
            </CardTitle>
            <CardDescription>
              Your account details and preferences
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Name</Label>
                <Input value={session?.user?.name || 'Admin User'} disabled />
              </div>
              <div>
                <Label>Email</Label>
                <Input value={session?.user?.email || '<EMAIL>'} disabled />
              </div>
            </div>
            <div>
              <Label>Role</Label>
              <div className="mt-1">
                <Badge variant="default">{session?.user?.role || 'ADMIN'}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* System Configuration */}
        <div className="space-y-4">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">System Configuration</h2>
            <p className="text-muted-foreground">
              Overview of your CMS integrations and services
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            {configSections.map((section) => (
              <Card key={section.title}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <section.icon className="h-5 w-5" />
                      {section.title}
                    </div>
                    {getStatusBadge(section.status)}
                  </CardTitle>
                  <CardDescription>
                    {section.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {section.items.map((item, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {item.status && getStatusIcon(item.status)}
                          <span className="text-sm font-medium">{item.label}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {'masked' in item && item.masked ? '••••••••••••' : item.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Environment Variables */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Environment Configuration
            </CardTitle>
            <CardDescription>
              Required environment variables for CMS functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid gap-4">
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">DATABASE_URL</div>
                    <div className="text-sm text-muted-foreground">PostgreSQL connection string</div>
                  </div>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">NEXTAUTH_SECRET</div>
                    <div className="text-sm text-muted-foreground">Authentication secret key</div>
                  </div>
                  <CheckCircle className="h-5 w-5 text-green-500" />
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">CLOUDINARY_*</div>
                    <div className="text-sm text-muted-foreground">Image upload configuration</div>
                  </div>
                  <AlertCircle className="h-5 w-5 text-yellow-500" />
                </div>
                
                <div className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">GOOGLE_ANALYTICS_*</div>
                    <div className="text-sm text-muted-foreground">Analytics integration</div>
                  </div>
                  <AlertCircle className="h-5 w-5 text-yellow-500" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>System Actions</CardTitle>
            <CardDescription>
              Maintenance and administrative actions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button variant="outline" disabled={loading}>
                Backup Database
              </Button>
              <Button variant="outline" disabled={loading}>
                Clear Cache
              </Button>
              <Button variant="outline" disabled={loading}>
                Export Data
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
