"use client";

import { motion } from "framer-motion";
import { <PERSON>, Rocket, Star } from "lucide-react";

export function ProjectsHero() {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-4xl mx-auto">
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="space-y-4">
              <motion.h1
                className="text-4xl sm:text-5xl lg:text-6xl font-bold"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                My <span className="gradient-text-blue">Projects</span>
              </motion.h1>
              
              <motion.p
                className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                A collection of projects that showcase my skills in full-stack development, 
                UI/UX design, and modern web technologies. Each project represents a unique 
                challenge and learning experience.
              </motion.p>
            </div>

            {/* Feature highlights */}
            <motion.div
              className="grid md:grid-cols-3 gap-8 mt-12"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <div className="flex flex-col items-center space-y-3">
                <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Code className="h-6 w-6 text-blue-500" />
                </div>
                <h3 className="font-semibold">Clean Code</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Well-structured, maintainable code following best practices and industry standards.
                </p>
              </div>

              <div className="flex flex-col items-center space-y-3">
                <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Rocket className="h-6 w-6 text-purple-500" />
                </div>
                <h3 className="font-semibold">Modern Tech</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Built with cutting-edge technologies like React, Next.js, TypeScript, and more.
                </p>
              </div>

              <div className="flex flex-col items-center space-y-3">
                <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                  <Star className="h-6 w-6 text-green-500" />
                </div>
                <h3 className="font-semibold">User-Focused</h3>
                <p className="text-sm text-muted-foreground text-center">
                  Designed with user experience in mind, ensuring accessibility and performance.
                </p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
