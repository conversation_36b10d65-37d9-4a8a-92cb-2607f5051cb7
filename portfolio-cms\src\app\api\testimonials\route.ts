import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const testimonials = await prisma.testimonial.findMany({
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        order: 'asc',
      },
    })

    return withCors(NextResponse.json(testimonials))
  } catch (error) {
    console.error('Error fetching testimonials:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch testimonials' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      role,
      company,
      content,
      avatar,
      rating,
      featured,
      published,
      order,
    } = body

    const testimonial = await prisma.testimonial.create({
      data: {
        name,
        role,
        company,
        content,
        avatar,
        rating: rating || 5,
        featured: featured || false,
        published: published !== undefined ? published : true,
        order: order || 0,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return withCors(NextResponse.json(testimonial, { status: 201 }))
  } catch (error) {
    console.error('Error creating testimonial:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create testimonial' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}
