import { <PERSON>ada<PERSON> } from "next";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { Blog<PERSON>ero } from "@/components/blog/blog-hero";
import { BlogGrid } from "@/components/blog/blog-grid";
import { BlogNewsletter } from "@/components/blog/blog-newsletter";

export const metadata: Metadata = {
  title: "Blog - Ashish Kamat",
  description: "Read my latest articles on web development, React, Next.js, TypeScript, and modern frontend technologies. Tips, tutorials, and insights from a full-stack developer.",
  openGraph: {
    title: "Blog - <PERSON><PERSON> Kamat",
    description: "Read my latest articles on web development, React, Next.js, TypeScript, and modern frontend technologies. Tips, tutorials, and insights from a full-stack developer.",
  },
};

export default function BlogPage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main className="pt-16">
        <BlogHero />
        <BlogGrid />
        <BlogNewsletter />
      </main>
      <Footer />
    </div>
  );
}
