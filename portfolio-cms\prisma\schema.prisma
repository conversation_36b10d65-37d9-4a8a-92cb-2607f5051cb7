// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  password  String
  role      Role     @default(ADMIN)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  projects     Project[]
  blogPosts    BlogPost[]
  testimonials Testimonial[]

  @@map("users")
}

enum Role {
  ADMIN
  EDITOR
}

// Project model
model Project {
  id              String   @id @default(cuid())
  title           String
  description     String
  longDescription String?
  image           String?
  category        String
  technologies    String[] // Array of technology names
  liveUrl         String?
  githubUrl       String?
  featured        <PERSON>olean  @default(false)
  published       Boolean  @default(true)
  order           Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("projects")
}

// Blog post model
model BlogPost {
  id          String   @id @default(cuid())
  title       String
  slug        String   @unique
  excerpt     String
  content     String
  image       String?
  category    String
  tags        String[] // Array of tag names
  published   Boolean  @default(false)
  featured    Boolean  @default(false)
  readTime    Int?     // Reading time in minutes
  views       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  publishedAt DateTime?

  // Relations
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("blog_posts")
}

// Service model
model Service {
  id          String   @id @default(cuid())
  title       String
  description String
  features    String[] // Array of feature descriptions
  icon        String   // Icon name or class
  color       String   // Color class for styling
  bgColor     String   // Background color class
  order       Int      @default(0)
  published   Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("services")
}

// Technology stack model
model TechStack {
  id        String   @id @default(cuid())
  name      String
  logo      String   // Logo image URL
  color     String   // Color class for styling
  category  String   // frontend, backend, tools, etc.
  order     Int      @default(0)
  published Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("tech_stack")
}

// Testimonial model
model Testimonial {
  id        String   @id @default(cuid())
  name      String
  role      String
  company   String
  content   String
  avatar    String?
  rating    Int      @default(5)
  featured  Boolean  @default(false)
  published Boolean  @default(true)
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  authorId String
  author   User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("testimonials")
}

// Analytics model for storing Google Analytics data
model Analytics {
  id          String   @id @default(cuid())
  date        DateTime
  pageViews   Int      @default(0)
  sessions    Int      @default(0)
  users       Int      @default(0)
  bounceRate  Float    @default(0)
  avgDuration Float    @default(0)
  topPages    Json?    // Store top pages data as JSON
  topSources  Json?    // Store traffic sources as JSON
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([date])
  @@map("analytics")
}
