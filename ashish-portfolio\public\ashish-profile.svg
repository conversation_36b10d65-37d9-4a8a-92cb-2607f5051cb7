<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="400" fill="url(#bg)"/>
  
  <!-- Person silhouette -->
  <circle cx="200" cy="150" r="60" fill="rgba(255,255,255,0.9)"/>
  <path d="M200 220 C160 220, 120 250, 120 300 L120 400 L280 400 L280 300 C280 250, 240 220, 200 220 Z" fill="rgba(255,255,255,0.9)"/>
  
  <!-- Initials -->
  <text x="200" y="165" font-family="Arial, sans-serif" font-size="36" font-weight="bold" text-anchor="middle" fill="#667eea">AK</text>
</svg>
