(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__dc15d093._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/middleware.js [middleware-edge] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$middleware$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["withAuth"])(function middleware(req) {
// Add any additional middleware logic here
}, {
    callbacks: {
        authorized: ({ token, req })=>{
            // Protect dashboard routes
            if (req.nextUrl.pathname.startsWith('/dashboard')) {
                return !!token;
            }
            // Allow public access to API routes for portfolio website
            if (req.nextUrl.pathname.startsWith('/api') && !req.nextUrl.pathname.startsWith('/api/auth')) {
                // Check if request is from portfolio website or has CORS headers
                const origin = req.headers.get('origin');
                const referer = req.headers.get('referer');
                // Allow public access to blog posts by slug (for portfolio website)
                const blogSlugPattern = /^\/api\/blog\/[^\/]+$/;
                if (blogSlugPattern.test(req.nextUrl.pathname)) {
                    // Check if this looks like a slug (not a CUID)
                    const pathParts = req.nextUrl.pathname.split('/');
                    const identifier = pathParts[pathParts.length - 1];
                    const isSlug = !identifier.startsWith('c') || identifier.length <= 20;
                    if (isSlug) {
                        return true // Allow public access for slug-based requests
                        ;
                    }
                }
                // Allow requests from portfolio website
                if (origin === 'http://localhost:3000' || referer?.startsWith('http://localhost:3000') || req.method === 'OPTIONS') {
                    return true;
                }
                // Require authentication for CMS dashboard API access
                return !!token;
            }
            return true;
        }
    }
});
const config = {
    matcher: [
        '/dashboard/:path*',
        '/api/:path*'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__dc15d093._.js.map