{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/api/:path*{(\\\\.json)}?", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "cf4ca84f96eef77f5f49906d8ba346e9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0ac639d03780d407f178b9a1dcd4cd9414acbfbc7514c23127d24805fddf78e3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7a39ebbc356a70533cbb7c119ea6a0b83e446864a71f7c059636e4158ce1dc64"}}}, "instrumentation": null, "functions": {}}