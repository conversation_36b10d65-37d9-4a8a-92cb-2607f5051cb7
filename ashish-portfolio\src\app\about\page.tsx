import { Metada<PERSON> } from "next";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { AboutHero } from "@/components/about/about-hero";
import { Experience } from "@/components/about/experience";
import { Skills } from "@/components/about/skills";
import { Education } from "@/components/about/education";

export const metadata: Metadata = {
  title: "About - Ashish Kamat",
  description: "Learn more about <PERSON><PERSON>, a passionate full-stack developer and UI/UX designer with expertise in modern web technologies.",
  openGraph: {
    title: "About - Ashish Kamat",
    description: "Learn more about <PERSON><PERSON>, a passionate full-stack developer and UI/UX designer with expertise in modern web technologies.",
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main className="pt-16">
        <AboutHero />
        <Experience />
        <Skills />
        <Education />
      </main>
      <Footer />
    </div>
  );
}
