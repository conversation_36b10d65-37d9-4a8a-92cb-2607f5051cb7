import { useQuery } from '@tanstack/react-query'
import { api } from './api'

// Query keys
export const queryKeys = {
  projects: ['projects'] as const,
  project: (id: string) => ['projects', id] as const,
  blogPosts: ['blogPosts'] as const,
  blogPost: (id: string) => ['blogPosts', id] as const,
  services: ['services'] as const,
  techStack: ['techStack'] as const,
  testimonials: ['testimonials'] as const,
}

// Project queries
export const useProjects = () => {
  return useQuery({
    queryKey: queryKeys.projects,
    queryFn: api.getProjects,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
}

export const useProject = (id: string) => {
  return useQuery({
    queryKey: queryKeys.project(id),
    queryFn: () => api.getProject(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Blog queries
export const useBlogPosts = () => {
  return useQuery({
    queryKey: queryKeys.blogPosts,
    queryFn: api.getBlogPosts,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

export const useBlogPost = (id: string) => {
  return useQuery({
    queryKey: queryKeys.blogPost(id),
    queryFn: () => api.getBlogPost(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  })
}

// Service queries
export const useServices = () => {
  return useQuery({
    queryKey: queryKeys.services,
    queryFn: api.getServices,
    staleTime: 10 * 60 * 1000, // 10 minutes (services change less frequently)
    gcTime: 30 * 60 * 1000, // 30 minutes
  })
}

// Tech stack queries
export const useTechStack = () => {
  return useQuery({
    queryKey: queryKeys.techStack,
    queryFn: api.getTechStack,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  })
}

// Testimonial queries
export const useTestimonials = () => {
  return useQuery({
    queryKey: queryKeys.testimonials,
    queryFn: api.getTestimonials,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  })
}
