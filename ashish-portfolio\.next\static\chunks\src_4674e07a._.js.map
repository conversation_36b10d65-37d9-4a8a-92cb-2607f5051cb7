{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/command-palette.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Search, \n  Home, \n  User, \n  Briefcase, \n  Mail, \n  FileText,\n  ExternalLink,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface Command {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  action: () => void;\n  category: string;\n  keywords: string[];\n}\n\ninterface CommandPaletteProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {\n  const [search, setSearch] = useState(\"\");\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const router = useRouter();\n\n  const commands: Command[] = [\n    // Navigation\n    {\n      id: \"home\",\n      title: \"Home\",\n      description: \"Go to homepage\",\n      icon: Home,\n      action: () => router.push(\"/\"),\n      category: \"Navigation\",\n      keywords: [\"home\", \"main\", \"landing\"],\n    },\n    {\n      id: \"about\",\n      title: \"About\",\n      description: \"Learn more about me\",\n      icon: User,\n      action: () => router.push(\"/about\"),\n      category: \"Navigation\",\n      keywords: [\"about\", \"bio\", \"experience\", \"skills\"],\n    },\n    {\n      id: \"projects\",\n      title: \"Projects\",\n      description: \"View my work and projects\",\n      icon: Briefcase,\n      action: () => router.push(\"/projects\"),\n      category: \"Navigation\",\n      keywords: [\"projects\", \"work\", \"portfolio\", \"showcase\"],\n    },\n    {\n      id: \"blog\",\n      title: \"Blog\",\n      description: \"Read my latest articles\",\n      icon: FileText,\n      action: () => router.push(\"/blog\"),\n      category: \"Navigation\",\n      keywords: [\"blog\", \"articles\", \"writing\", \"posts\"],\n    },\n    {\n      id: \"contact\",\n      title: \"Contact\",\n      description: \"Get in touch with me\",\n      icon: Mail,\n      action: () => router.push(\"/contact\"),\n      category: \"Navigation\",\n      keywords: [\"contact\", \"email\", \"message\", \"hire\"],\n    },\n    // External Links\n    {\n      id: \"github\",\n      title: \"GitHub\",\n      description: \"View my GitHub profile\",\n      icon: Github,\n      action: () => window.open(\"https://github.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"github\", \"code\", \"repositories\", \"open source\"],\n    },\n    {\n      id: \"linkedin\",\n      title: \"LinkedIn\",\n      description: \"Connect with me on LinkedIn\",\n      icon: Linkedin,\n      action: () => window.open(\"https://linkedin.com/in/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"linkedin\", \"professional\", \"network\", \"career\"],\n    },\n    {\n      id: \"twitter\",\n      title: \"Twitter\",\n      description: \"Follow me on Twitter\",\n      icon: Twitter,\n      action: () => window.open(\"https://twitter.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"twitter\", \"social\", \"updates\", \"thoughts\"],\n    },\n    // Quick Actions\n    {\n      id: \"email\",\n      title: \"Send Email\",\n      description: \"Send me an email directly\",\n      icon: Mail,\n      action: () => window.open(\"mailto:<EMAIL>\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"email\", \"contact\", \"message\", \"hire\"],\n    },\n    {\n      id: \"resume\",\n      title: \"Download Resume\",\n      description: \"Download my latest resume\",\n      icon: ExternalLink,\n      action: () => window.open(\"/resume.pdf\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"resume\", \"cv\", \"download\", \"hire\"],\n    },\n  ];\n\n  const filteredCommands = commands.filter((command) => {\n    const searchLower = search.toLowerCase();\n    return (\n      command.title.toLowerCase().includes(searchLower) ||\n      command.description.toLowerCase().includes(searchLower) ||\n      command.keywords.some((keyword) => keyword.includes(searchLower))\n    );\n  });\n\n  const groupedCommands = filteredCommands.reduce((acc, command) => {\n    if (!acc[command.category]) {\n      acc[command.category] = [];\n    }\n    acc[command.category].push(command);\n    return acc;\n  }, {} as Record<string, Command[]>);\n\n  useEffect(() => {\n    setSelectedIndex(0);\n  }, [search]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev < filteredCommands.length - 1 ? prev + 1 : 0\n        );\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev > 0 ? prev - 1 : filteredCommands.length - 1\n        );\n      } else if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (filteredCommands[selectedIndex]) {\n          filteredCommands[selectedIndex].action();\n          onOpenChange(false);\n          setSearch(\"\");\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [open, selectedIndex, filteredCommands, onOpenChange]);\n\n  const handleCommandSelect = (command: Command) => {\n    command.action();\n    onOpenChange(false);\n    setSearch(\"\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl p-0 overflow-hidden\">\n        <DialogHeader className=\"p-4 pb-0\">\n          <DialogTitle className=\"sr-only\">Command Palette</DialogTitle>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Type a command or search...\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"pl-10 border-0 focus-visible:ring-0 text-base\"\n              autoFocus\n            />\n          </div>\n        </DialogHeader>\n\n        <div className=\"max-h-96 overflow-y-auto p-4 pt-0\">\n          {Object.keys(groupedCommands).length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No commands found for \"{search}\"\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {Object.entries(groupedCommands).map(([category, commands]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2\">\n                    {category}\n                  </div>\n                  <div className=\"space-y-1\">\n                    {commands.map((command, index) => {\n                      const globalIndex = filteredCommands.indexOf(command);\n                      const Icon = command.icon;\n                      \n                      return (\n                        <motion.button\n                          key={command.id}\n                          onClick={() => handleCommandSelect(command)}\n                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${\n                            globalIndex === selectedIndex\n                              ? \"bg-accent text-accent-foreground\"\n                              : \"hover:bg-accent/50\"\n                          }`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <div className={`p-2 rounded-md ${\n                            globalIndex === selectedIndex\n                              ? \"bg-primary text-primary-foreground\"\n                              : \"bg-muted\"\n                          }`}>\n                            <Icon className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"font-medium\">{command.title}</div>\n                            <div className=\"text-sm text-muted-foreground truncate\">\n                              {command.description}\n                            </div>\n                          </div>\n                          {command.category === \"Social\" && (\n                            <ExternalLink className=\"h-3 w-3 text-muted-foreground\" />\n                          )}\n                        </motion.button>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-t p-3 text-xs text-muted-foreground flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↑↓</Badge>\n              <span>Navigate</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↵</Badge>\n              <span>Select</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">Esc</Badge>\n              <span>Close</span>\n            </div>\n          </div>\n          <div className=\"text-muted-foreground/60\">\n            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;;;AAxBA;;;;;;;;AAyCO,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAuB;;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAsB;QAC1B,aAAa;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAQ;aAAU;QACvC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAO;gBAAc;aAAS;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,+MAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAQ;gBAAa;aAAW;QACzD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,iNAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAY;gBAAW;aAAQ;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAS;gBAAW;aAAO;QACnD;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yMAAA,CAAA,SAAM;YACZ,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAQ;gBAAgB;aAAc;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,6MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC,uCAAuC;YACjE,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAgB;gBAAW;aAAS;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2MAAA,CAAA,UAAO;YACb,QAAQ,IAAM,OAAO,IAAI,CAAC,mCAAmC;YAC7D,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAU;gBAAW;aAAW;QACxD;QACA,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC,gCAAgC;YAC1D,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAW;gBAAW;aAAO;QACnD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,yNAAA,CAAA,eAAY;YAClB,QAAQ,IAAM,OAAO,IAAI,CAAC,eAAe;YACzC,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAM;gBAAY;aAAO;QAChD;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,OAAO,WAAW;QACtC,OACE,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,QAAQ,CAAC;IAExD;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;QAC5B;QACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IAEJ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,iBAAiB;QACnB;mCAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB,CAAC;oBACrB,IAAI,CAAC,MAAM;oBAEX,IAAI,EAAE,GAAG,KAAK,aAAa;wBACzB,EAAE,cAAc;wBAChB;sEAAiB,CAAC,OAChB,OAAO,iBAAiB,MAAM,GAAG,IAAI,OAAO,IAAI;;oBAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;wBAC9B,EAAE,cAAc;wBAChB;sEAAiB,CAAC,OAChB,OAAO,IAAI,OAAO,IAAI,iBAAiB,MAAM,GAAG;;oBAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;wBAC5B,EAAE,cAAc;wBAChB,IAAI,gBAAgB,CAAC,cAAc,EAAE;4BACnC,gBAAgB,CAAC,cAAc,CAAC,MAAM;4BACtC,aAAa;4BACb,UAAU;wBACZ;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;4CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;mCAAG;QAAC;QAAM;QAAe;QAAkB;KAAa;IAExD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,MAAM;QACd,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAU;;;;;;sCACjC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,SAAS;;;;;;;;;;;;;;;;;;8BAKf,6LAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,kBACvC,6LAAC;wBAAI,WAAU;;4BAAyC;4BAC9B;4BAAO;;;;;;6CAGjC,6LAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBACxD,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4CACtB,MAAM,cAAc,iBAAiB,OAAO,CAAC;4CAC7C,MAAM,OAAO,QAAQ,IAAI;4CAEzB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,gBACZ,qCACA,sBACJ;gDACF,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,6LAAC;wDAAI,WAAW,CAAC,eAAe,EAC9B,gBAAgB,gBACZ,uCACA,YACJ;kEACA,cAAA,6LAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,QAAQ,KAAK,0BACpB,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAxBrB,QAAQ,EAAE;;;;;wCA4BrB;;;;;;;+BAvCM;;;;;;;;;;;;;;;8BA+ClB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,6LAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM;gCAAC;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMnF;GA1PgB;;QAGC,qIAAA,CAAA,YAAS;;;KAHV", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useTheme } from \"next-themes\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Moon,\n  Sun,\n  Menu,\n  X,\n  Home,\n  User,\n  Briefcase,\n  Mail,\n  FileText,\n  Search\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { CommandPalette } from \"@/components/command-palette\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\", icon: Home },\n  { name: \"About\", href: \"/about\", icon: User },\n  { name: \"Projects\", href: \"/projects\", icon: Briefcase },\n  { name: \"Blog\", href: \"/blog\", icon: FileText },\n  { name: \"Contact\", href: \"/contact\", icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault();\n        setCommandPaletteOpen(true);\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, []);\n\n  const toggleTheme = () => {\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue\">\n              AK\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent\"\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Command Palette, Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setCommandPaletteOpen(true)}\n              className=\"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground\"\n            >\n              <Search className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Search</span>\n              <kbd className=\"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100\">\n                <span className=\"text-xs\">⌘</span>K\n              </kbd>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={toggleTheme}\n              className=\"w-9 h-9\"\n            >\n              {theme === \"dark\" ? (\n                <Sun className=\"h-4 w-4\" />\n              ) : (\n                <Moon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"w-9 h-9\"\n              >\n                {isOpen ? (\n                  <X className=\"h-4 w-4\" />\n                ) : (\n                  <Menu className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border\">\n                {navItems.map((item) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Icon className=\"h-4 w-4 mr-3\" />\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n\n      <CommandPalette\n        open={commandPaletteOpen}\n        onOpenChange={setCommandPaletteOpen}\n      />\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;;;AApBA;;;;;;;;;AAsBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,sMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,qMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,+MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,iNAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,qMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,WAAW;QACb;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;qDAAe;oBACnB,YAAY,OAAO,OAAO,GAAG;gBAC/B;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;sDAAgB,CAAC;oBACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB,sBAAsB;oBACxB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;+BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WACI,6DACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwC;;;;;;;;;;;0CAMnE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CARP,KAAK,IAAI;;;;;;;;;;;;;;;0CAgBtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;;;;;;kDAItC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAU;sDAET,uBACC,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,6LAAC,4LAAA,CAAA,kBAAe;kCACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;;8DAEzB,6LAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAVP,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC,2IAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB;GA7KgB;;QAIc,mJAAA,CAAA,WAAQ;;;KAJtB", "debugId": null}}, {"offset": {"line": 1309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code,\n  Coffee\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst footerLinks = {\n  navigation: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"About\", href: \"/about\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  services: [\n    { name: \"Full Stack Development\", href: \"/#services\" },\n    { name: \"UI/UX Design\", href: \"/#services\" },\n    { name: \"Mobile Development\", href: \"/#services\" },\n    { name: \"Consulting\", href: \"/#services\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Tech Stack\", href: \"/#tech-stack\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    href: \"https://github.com/ashishkamat\",\n    icon: Github,\n    color: \"hover:text-gray-600 dark:hover:text-gray-300\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat\",\n    icon: Twitter,\n    color: \"hover:text-blue-500\",\n  },\n  {\n    name: \"Email\",\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    color: \"hover:text-green-600\",\n  },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-background border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue mb-4 inline-block\">\n                  Ashish Kamat\n                </Link>\n                <p className=\"text-muted-foreground mb-6 max-w-md\">\n                  Full Stack Developer & UI/UX Designer passionate about creating \n                  innovative digital experiences that make a difference.\n                </p>\n                \n                {/* Social Links */}\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => {\n                    const Icon = social.icon;\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className={`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                      >\n                        <Icon className=\"h-5 w-5\" />\n                      </motion.a>\n                    );\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Navigation Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Navigation</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.navigation.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Services Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          className=\"py-6 border-t border-border\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>© 2024 Ashish Kamat. Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 animate-pulse\" />\n              <span>and</span>\n              <Coffee className=\"h-4 w-4 text-amber-600\" />\n              <span>in Kathmandu</span>\n            </div>\n\n            {/* Tech Stack & Back to Top */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <Code className=\"h-4 w-4\" />\n                <span>Built with Next.js & Tailwind CSS</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={scrollToTop}\n                className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Fun Easter Egg */}\n        <motion.div\n          className=\"text-center py-4 border-t border-border/50\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <p className=\"text-xs text-muted-foreground/70\">\n            🚀 This website is powered by{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% coffee\n            </span>{\" \"}\n            and{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% passion\n            </span>\n          </p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAA0B,MAAM;QAAa;QACrD;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAc,MAAM;QAAa;KAC1C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAe;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,6MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA0D;;;;;;sDAGnF,6LAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAMnD,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,oEAAoE,EAAE,OAAO,KAAK,EAAE;oDAChG,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,6LAAC;wDAAK,WAAU;;;;;;mDAZX,OAAO,IAAI;;;;;4CAetB;;;;;;;;;;;;;;;;;0CAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;kDACN,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAK;;;;;;;;;;;;0CAIR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAGR,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,6LAAC;wBAAE,WAAU;;4BAAmC;4BAChB;0CAC9B,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;4BAC3B;4BAAI;4BACR;0CACJ,6LAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;KApLgB", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1961, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-header.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { Calendar, Clock, Eye, ArrowLeft, Share2, Bookmark } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { type BlogPost } from \"@/lib/api\";\n\ninterface BlogPostHeaderProps {\n  post: BlogPost;\n}\n\nexport function BlogPostHeader({ post }: BlogPostHeaderProps) {\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const handleShare = async () => {\n    if (navigator.share) {\n      try {\n        await navigator.share({\n          title: post.title,\n          text: post.excerpt,\n          url: window.location.href,\n        });\n      } catch (error) {\n        console.log('Error sharing:', error);\n      }\n    } else {\n      // Fallback to copying URL\n      navigator.clipboard.writeText(window.location.href);\n    }\n  };\n\n  return (\n    <header className=\"bg-muted/30 py-12 lg:py-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Back to Blog */}\n          <motion.div\n            className=\"mb-8\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5 }}\n          >\n            <Button variant=\"ghost\" asChild className=\"group\">\n              <Link href=\"/blog\">\n                <ArrowLeft className=\"mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-300\" />\n                Back to Blog\n              </Link>\n            </Button>\n          </motion.div>\n\n          {/* Category Badge */}\n          <motion.div\n            className=\"mb-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n          >\n            <Badge variant=\"secondary\" className=\"text-sm\">\n              {post.category}\n            </Badge>\n          </motion.div>\n\n          {/* Title */}\n          <motion.h1\n            className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 leading-tight\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n          >\n            {post.title}\n          </motion.h1>\n\n          {/* Excerpt */}\n          <motion.p\n            className=\"text-xl text-muted-foreground mb-8 leading-relaxed\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n          >\n            {post.excerpt}\n          </motion.p>\n\n          {/* Meta Information */}\n          <motion.div\n            className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.4 }}\n          >\n            {/* Author and Date */}\n            <div className=\"flex items-center space-x-4\">\n              <Avatar className=\"h-12 w-12\">\n                <AvatarImage src=\"/ashish-profile.svg\" alt=\"Ashish Kamat\" />\n                <AvatarFallback>\n                  AK\n                </AvatarFallback>\n              </Avatar>\n              <div>\n                <div className=\"font-semibold\">Ashish Kamat</div>\n                <div className=\"text-sm text-muted-foreground\">Full Stack Developer & UI/UX Designer</div>\n              </div>\n            </div>\n\n            {/* Post Meta */}\n            <div className=\"flex items-center space-x-6 text-sm text-muted-foreground\">\n              <div className=\"flex items-center space-x-1\">\n                <Calendar className=\"h-4 w-4\" />\n                <span>{formatDate(post.publishedAt)}</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <Clock className=\"h-4 w-4\" />\n                <span>{post.readTime || '5 min read'}</span>\n              </div>\n              <div className=\"flex items-center space-x-1\">\n                <Eye className=\"h-4 w-4\" />\n                <span>{post.views.toLocaleString()}</span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Tags and Actions */}\n          <motion.div\n            className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6 mt-8 pt-8 border-t border-border\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.5 }}\n          >\n            {/* Tags */}\n            <div className=\"flex flex-wrap gap-2\">\n              {post.tags.map((tag) => (\n                <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                  #{tag}\n                </Badge>\n              ))}\n            </div>\n\n            {/* Actions */}\n            <div className=\"flex items-center space-x-2\">\n              <Button variant=\"outline\" size=\"sm\" onClick={handleShare} className=\"group\">\n                <Share2 className=\"mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-300\" />\n                Share\n              </Button>\n              <Button variant=\"outline\" size=\"sm\" className=\"group\">\n                <Bookmark className=\"mr-2 h-4 w-4 group-hover:scale-110 transition-transform duration-300\" />\n                Save\n              </Button>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;AAeO,SAAS,eAAe,EAAE,IAAI,EAAuB;IAC1D,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,UAAU,KAAK,EAAE;YACnB,IAAI;gBACF,MAAM,UAAU,KAAK,CAAC;oBACpB,OAAO,KAAK,KAAK;oBACjB,MAAM,KAAK,OAAO;oBAClB,KAAK,OAAO,QAAQ,CAAC,IAAI;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,GAAG,CAAC,kBAAkB;YAChC;QACF,OAAO;YACL,0BAA0B;YAC1B,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,IAAI;QACpD;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,OAAO;4BAAC,WAAU;sCACxC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;;kDACT,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAA8E;;;;;;;;;;;;;;;;;kCAOzG,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAClC,KAAK,QAAQ;;;;;;;;;;;kCAKlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAEvC,KAAK,KAAK;;;;;;kCAIb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAEvC,KAAK,OAAO;;;;;;kCAIf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAI;gDAAsB,KAAI;;;;;;0DAC3C,6LAAC,qIAAA,CAAA,iBAAc;0DAAC;;;;;;;;;;;;kDAIlB,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAM,WAAW,KAAK,WAAW;;;;;;;;;;;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAM,KAAK,QAAQ,IAAI;;;;;;;;;;;;kDAE1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;0DAAM,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAGxC,6LAAC;gCAAI,WAAU;0CACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,6LAAC,oIAAA,CAAA,QAAK;wCAAW,SAAQ;wCAAU,WAAU;;4CAAU;4CACnD;;uCADQ;;;;;;;;;;0CAOhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;wCAAa,WAAU;;0DAClE,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAyE;;;;;;;kDAG7F,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;;0DAC5C,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAyE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7G;KAnJgB", "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Progress({\n  className,\n  value,\n  ...props\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\n  return (\n    <ProgressPrimitive.Root\n      data-slot=\"progress\"\n      className={cn(\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    >\n      <ProgressPrimitive.Indicator\n        data-slot=\"progress-indicator\"\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n      />\n    </ProgressPrimitive.Root>\n  )\n}\n\nexport { Progress }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 2445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-content.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect, useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport ReactMarkdown from \"react-markdown\";\nimport remarkGfm from \"remark-gfm\";\nimport rehypeHighlight from \"rehype-highlight\";\nimport rehypeRaw from \"rehype-raw\";\nimport \"highlight.js/styles/github-dark.css\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { type BlogPost } from \"@/lib/api\";\n\n// Function to detect if content is HTML (from rich text editor) or Markdown\nfunction isHtmlContent(content: string): boolean {\n  // Check for common HTML tags that wouldn't be in markdown\n  const htmlTags = /<(div|span|p|h[1-6]|strong|em|ul|ol|li|table|tr|td|th|img|a|blockquote|pre|code)[^>]*>/i;\n  return htmlTags.test(content);\n}\n\n// Function to safely render HTML content\nfunction renderHtmlContent(content: string) {\n  return (\n    <div\n      className=\"prose prose-lg dark:prose-invert max-w-none prose-headings:font-bold prose-headings:text-foreground prose-p:text-muted-foreground prose-p:leading-relaxed prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-strong:text-foreground prose-strong:font-semibold prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-code:text-sm prose-pre:bg-muted prose-pre:border prose-pre:border-border prose-blockquote:border-l-4 prose-blockquote:border-primary prose-blockquote:pl-4 prose-blockquote:italic prose-ul:list-disc prose-ol:list-decimal prose-li:text-muted-foreground prose-table:border-collapse prose-table:border prose-table:border-border prose-th:border prose-th:border-border prose-th:bg-muted prose-th:p-2 prose-th:font-semibold prose-td:border prose-td:border-border prose-td:p-2\"\n      dangerouslySetInnerHTML={{ __html: content }}\n    />\n  );\n}\n\ninterface BlogPostContentProps {\n  post: BlogPost;\n}\n\nexport function BlogPostContent({ post }: BlogPostContentProps) {\n  const [readingProgress, setReadingProgress] = useState(0);\n\n  useEffect(() => {\n    const updateReadingProgress = () => {\n      const scrollTop = window.scrollY;\n      const docHeight = document.documentElement.scrollHeight - window.innerHeight;\n      const progress = (scrollTop / docHeight) * 100;\n      setReadingProgress(Math.min(100, Math.max(0, progress)));\n    };\n\n    window.addEventListener(\"scroll\", updateReadingProgress);\n    return () => window.removeEventListener(\"scroll\", updateReadingProgress);\n  }, []);\n\n  return (\n    <div className=\"relative\">\n      {/* Reading Progress */}\n      <div className=\"fixed top-16 left-0 right-0 z-40 bg-background/80 backdrop-blur-sm border-b border-border\">\n        <Progress value={readingProgress} className=\"h-1 rounded-none\" />\n      </div>\n\n      {/* Article Content */}\n      <motion.article\n        className=\"prose prose-lg dark:prose-invert max-w-none\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.8 }}\n      >\n        {isHtmlContent(post.content) ? (\n          renderHtmlContent(post.content)\n        ) : (\n          <ReactMarkdown\n            remarkPlugins={[remarkGfm]}\n            rehypePlugins={[rehypeHighlight, rehypeRaw]}\n            components={{\n              h1: ({ children }) => (\n                <h1 className=\"text-3xl font-bold mt-12 mb-6 first:mt-0 scroll-mt-20\">\n                  {children}\n                </h1>\n              ),\n              h2: ({ children }) => (\n                <h2 className=\"text-2xl font-bold mt-10 mb-4 scroll-mt-20\">\n                  {children}\n                </h2>\n              ),\n              h3: ({ children }) => (\n                <h3 className=\"text-xl font-bold mt-8 mb-3 scroll-mt-20\">\n                  {children}\n                </h3>\n              ),\n              p: ({ children }) => (\n                <p className=\"mb-6 leading-relaxed text-muted-foreground\">\n                  {children}\n                </p>\n              ),\n            ul: ({ children }) => (\n              <ul className=\"mb-6 space-y-2 list-disc list-inside\">\n                {children}\n              </ul>\n            ),\n            ol: ({ children }) => (\n              <ol className=\"mb-6 space-y-2 list-decimal list-inside\">\n                {children}\n              </ol>\n            ),\n            li: ({ children }) => (\n              <li className=\"text-muted-foreground\">\n                {children}\n              </li>\n            ),\n            blockquote: ({ children }) => (\n              <blockquote className=\"border-l-4 border-primary pl-6 my-6 italic text-muted-foreground bg-muted/50 py-4 rounded-r-lg\">\n                {children}\n              </blockquote>\n            ),\n            code: ({ inline, className, children, ...props }) => {\n              const match = /language-(\\w+)/.exec(className || '');\n              \n              if (!inline && match) {\n                return (\n                  <div className=\"relative group\">\n                    <div className=\"absolute top-3 right-3 text-xs text-muted-foreground bg-muted px-2 py-1 rounded\">\n                      {match[1]}\n                    </div>\n                    <pre className=\"bg-muted p-4 rounded-lg overflow-x-auto border border-border my-6\">\n                      <code className={className} {...props}>\n                        {children}\n                      </code>\n                    </pre>\n                  </div>\n                );\n              }\n              \n              return (\n                <code \n                  className=\"bg-muted px-1.5 py-0.5 rounded text-sm font-mono border border-border\" \n                  {...props}\n                >\n                  {children}\n                </code>\n              );\n            },\n            pre: ({ children }) => (\n              <div className=\"not-prose\">\n                {children}\n              </div>\n            ),\n            a: ({ href, children }) => (\n              <a \n                href={href}\n                className=\"text-primary hover:text-primary/80 underline underline-offset-4 transition-colors duration-200\"\n                target={href?.startsWith('http') ? '_blank' : undefined}\n                rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}\n              >\n                {children}\n              </a>\n            ),\n            img: ({ src, alt }) => (\n              <div className=\"my-8\">\n                <img \n                  src={src} \n                  alt={alt} \n                  className=\"rounded-lg border border-border w-full\"\n                />\n                {alt && (\n                  <p className=\"text-center text-sm text-muted-foreground mt-2 italic\">\n                    {alt}\n                  </p>\n                )}\n              </div>\n            ),\n            table: ({ children }) => (\n              <div className=\"overflow-x-auto my-6\">\n                <table className=\"w-full border-collapse border border-border rounded-lg\">\n                  {children}\n                </table>\n              </div>\n            ),\n            th: ({ children }) => (\n              <th className=\"border border-border px-4 py-2 bg-muted font-semibold text-left\">\n                {children}\n              </th>\n            ),\n            td: ({ children }) => (\n              <td className=\"border border-border px-4 py-2\">\n                {children}\n              </td>\n            ),\n            hr: () => (\n              <hr className=\"my-8 border-border\" />\n            ),\n          }}\n        >\n          {post.content}\n        </ReactMarkdown>\n        )}\n      </motion.article>\n\n      {/* Table of Contents (if needed) */}\n      <div className=\"mt-12 p-6 bg-muted/50 rounded-lg border border-border\">\n        <h3 className=\"font-semibold mb-4\">Article Summary</h3>\n        <p className=\"text-sm text-muted-foreground\">\n          This article covers building scalable React applications with TypeScript, \n          including project structure, component patterns, state management, \n          performance optimization, and testing strategies.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AATA;;;;;;;;;AAYA,4EAA4E;AAC5E,SAAS,cAAc,OAAe;IACpC,0DAA0D;IAC1D,MAAM,WAAW;IACjB,OAAO,SAAS,IAAI,CAAC;AACvB;AAEA,yCAAyC;AACzC,SAAS,kBAAkB,OAAe;IACxC,qBACE,6LAAC;QACC,WAAU;QACV,yBAAyB;YAAE,QAAQ;QAAQ;;;;;;AAGjD;AAMO,SAAS,gBAAgB,EAAE,IAAI,EAAwB;;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;mEAAwB;oBAC5B,MAAM,YAAY,OAAO,OAAO;oBAChC,MAAM,YAAY,SAAS,eAAe,CAAC,YAAY,GAAG,OAAO,WAAW;oBAC5E,MAAM,WAAW,AAAC,YAAY,YAAa;oBAC3C,mBAAmB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;gBAC/C;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oBAAC,OAAO;oBAAiB,WAAU;;;;;;;;;;;0BAI9C,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;gBACb,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE3B,cAAc,KAAK,OAAO,IACzB,kBAAkB,KAAK,OAAO,kBAE9B,6LAAC,2LAAA,CAAA,UAAa;oBACZ,eAAe;wBAAC,gJAAA,CAAA,UAAS;qBAAC;oBAC1B,eAAe;wBAAC,sJAAA,CAAA,UAAe;wBAAE,gJAAA,CAAA,UAAS;qBAAC;oBAC3C,YAAY;wBACV,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;gCAAE,WAAU;0CACV;;;;;;wBAGP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;gCAAW,WAAU;0CACnB;;;;;;wBAGL,MAAM,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;4BAC9C,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;4BAEjD,IAAI,CAAC,UAAU,OAAO;gCACpB,qBACE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,KAAK,CAAC,EAAE;;;;;;sDAEX,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW;gDAAY,GAAG,KAAK;0DAClC;;;;;;;;;;;;;;;;;4BAKX;4BAEA,qBACE,6LAAC;gCACC,WAAU;gCACT,GAAG,KAAK;0CAER;;;;;;wBAGP;wBACA,KAAK,CAAC,EAAE,QAAQ,EAAE,iBAChB,6LAAC;gCAAI,WAAU;0CACZ;;;;;;wBAGL,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,iBACpB,6LAAC;gCACC,MAAM;gCACN,WAAU;gCACV,QAAQ,MAAM,WAAW,UAAU,WAAW;gCAC9C,KAAK,MAAM,WAAW,UAAU,wBAAwB;0CAEvD;;;;;;wBAGL,KAAK,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,iBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK;wCACL,KAAK;wCACL,WAAU;;;;;;oCAEX,qBACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;wBAKT,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;8CACd;;;;;;;;;;;wBAIP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;gCAAG,WAAU;0CACX;;;;;;wBAGL,IAAI,kBACF,6LAAC;gCAAG,WAAU;;;;;;oBAElB;8BAEC,KAAK,OAAO;;;;;;;;;;;0BAMjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;;AAQrD;GA1KgB;KAAA", "debugId": null}}, {"offset": {"line": 2796, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 2911, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 2947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Share2, \n  Bookmark, \n  Heart, \n  MessageCircle, \n  Twitter, \n  Linkedin, \n  Facebook,\n  Link as LinkIcon,\n  Calendar,\n  Tag,\n  User\n} from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { toast } from \"sonner\";\nimport { type BlogPost } from \"@/lib/api\";\n\ninterface BlogPostSidebarProps {\n  post: BlogPost;\n}\n\nexport function BlogPostSidebar({ post }: BlogPostSidebarProps) {\n  const [isBookmarked, setIsBookmarked] = useState(false);\n  const [isLiked, setIsLiked] = useState(false);\n  const [likes, setLikes] = useState(42);\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const handleBookmark = () => {\n    setIsBookmarked(!isBookmarked);\n    toast.success(isBookmarked ? \"Removed from bookmarks\" : \"Added to bookmarks\");\n  };\n\n  const handleLike = () => {\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n  };\n\n  const shareUrl = typeof window !== 'undefined' ? window.location.href : '';\n  const shareText = `Check out this article: ${post.title}`;\n\n  const socialShares = [\n    {\n      name: \"Twitter\",\n      icon: Twitter,\n      url: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`,\n      color: \"text-blue-500\"\n    },\n    {\n      name: \"LinkedIn\",\n      icon: Linkedin,\n      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`,\n      color: \"text-blue-600\"\n    },\n    {\n      name: \"Facebook\",\n      icon: Facebook,\n      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,\n      color: \"text-blue-700\"\n    }\n  ];\n\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(shareUrl);\n    toast.success(\"Link copied to clipboard!\");\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Actions */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Actions</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <Button\n                variant={isLiked ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={handleLike}\n                className=\"flex-1 mr-2\"\n              >\n                <Heart className={`mr-2 h-4 w-4 ${isLiked ? 'fill-current' : ''}`} />\n                {likes}\n              </Button>\n              <Button\n                variant={isBookmarked ? \"default\" : \"outline\"}\n                size=\"sm\"\n                onClick={handleBookmark}\n                className=\"flex-1 ml-2\"\n              >\n                <Bookmark className={`mr-2 h-4 w-4 ${isBookmarked ? 'fill-current' : ''}`} />\n                Save\n              </Button>\n            </div>\n            \n            <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n              <MessageCircle className=\"mr-2 h-4 w-4\" />\n              Comment\n            </Button>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Share */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.1 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg flex items-center\">\n              <Share2 className=\"mr-2 h-5 w-5\" />\n              Share Article\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-3 gap-2\">\n              {socialShares.map((social) => {\n                const Icon = social.icon;\n                return (\n                  <Button\n                    key={social.name}\n                    variant=\"outline\"\n                    size=\"sm\"\n                    asChild\n                    className=\"p-2\"\n                  >\n                    <a\n                      href={social.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className={`${social.color} hover:${social.color}`}\n                    >\n                      <Icon className=\"h-4 w-4\" />\n                    </a>\n                  </Button>\n                );\n              })}\n            </div>\n            \n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={copyToClipboard}\n              className=\"w-full\"\n            >\n              <LinkIcon className=\"mr-2 h-4 w-4\" />\n              Copy Link\n            </Button>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Article Info */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.2 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Article Info</CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-2 text-sm\">\n                <Calendar className=\"h-4 w-4 text-muted-foreground\" />\n                <span>Published {formatDate(post.publishedAt)}</span>\n              </div>\n              \n              <div className=\"flex items-center space-x-2 text-sm\">\n                <Tag className=\"h-4 w-4 text-muted-foreground\" />\n                <span>{post.category}</span>\n              </div>\n              \n              <div className=\"flex items-center space-x-2 text-sm\">\n                <User className=\"h-4 w-4 text-muted-foreground\" />\n                <span>{post.readTime}</span>\n              </div>\n            </div>\n            \n            <Separator />\n            \n            <div>\n              <h4 className=\"font-semibold mb-2\">Tags</h4>\n              <div className=\"flex flex-wrap gap-1\">\n                {post.tags.map((tag) => (\n                  <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                    {tag}\n                  </Badge>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Author */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.3 }}\n      >\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">About the Author</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex items-start space-x-4\">\n              <Avatar className=\"h-12 w-12\">\n                <AvatarImage src=\"/ashish-profile.svg\" alt=\"Ashish Kamat\" />\n                <AvatarFallback>\n                  AK\n                </AvatarFallback>\n              </Avatar>\n              <div className=\"flex-1\">\n                <h4 className=\"font-semibold\">Ashish Kamat</h4>\n                <p className=\"text-sm text-muted-foreground mb-3\">\n                  Full Stack Developer & UI/UX Designer\n                </p>\n                <Button variant=\"outline\" size=\"sm\" asChild>\n                  <Link href=\"/about\">\n                    View Profile\n                  </Link>\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Newsletter */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.4 }}\n      >\n        <Card className=\"bg-gradient-to-br from-primary/10 to-secondary/10\">\n          <CardHeader>\n            <CardTitle className=\"text-lg\">Stay Updated</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <p className=\"text-sm text-muted-foreground mb-4\">\n              Get the latest articles and tutorials delivered to your inbox.\n            </p>\n            <Button className=\"w-full\" asChild>\n              <Link href=\"/blog#newsletter\">\n                Subscribe Now\n              </Link>\n            </Button>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;AA8BO,SAAS,gBAAgB,EAAE,IAAI,EAAwB;;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,CAAC;QACjB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,eAAe,2BAA2B;IAC1D;IAEA,MAAM,aAAa;QACjB,WAAW,CAAC;QACZ,SAAS,CAAA,OAAQ,UAAU,OAAO,IAAI,OAAO;IAC/C;IAEA,MAAM,WAAW,uCAAgC,OAAO,QAAQ,CAAC,IAAI;IACrE,MAAM,YAAY,CAAC,wBAAwB,EAAE,KAAK,KAAK,EAAE;IAEzD,MAAM,eAAe;QACnB;YACE,MAAM;YACN,MAAM,2MAAA,CAAA,UAAO;YACb,KAAK,CAAC,sCAAsC,EAAE,mBAAmB,WAAW,KAAK,EAAE,mBAAmB,WAAW;YACjH,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,KAAK,CAAC,oDAAoD,EAAE,mBAAmB,WAAW;YAC1F,OAAO;QACT;QACA;YACE,MAAM;YACN,MAAM,6MAAA,CAAA,WAAQ;YACd,KAAK,CAAC,6CAA6C,EAAE,mBAAmB,WAAW;YACnF,OAAO;QACT;KACD;IAED,MAAM,kBAAkB;QACtB,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,UAAU,YAAY;4CAC/B,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAW,CAAC,aAAa,EAAE,UAAU,iBAAiB,IAAI;;;;;;gDAChE;;;;;;;sDAEH,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,eAAe,YAAY;4CACpC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAW,CAAC,aAAa,EAAE,eAAe,iBAAiB,IAAI;;;;;;gDAAI;;;;;;;;;;;;;8CAKjF,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;;sDAC5C,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAIvC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC;wCACjB,MAAM,OAAO,OAAO,IAAI;wCACxB,qBACE,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAQ;4CACR,MAAK;4CACL,OAAO;4CACP,WAAU;sDAEV,cAAA,6LAAC;gDACC,MAAM,OAAO,GAAG;gDAChB,QAAO;gDACP,KAAI;gDACJ,WAAW,GAAG,OAAO,KAAK,CAAC,OAAO,EAAE,OAAO,KAAK,EAAE;0DAElD,cAAA,6LAAC;oDAAK,WAAU;;;;;;;;;;;2CAZb,OAAO,IAAI;;;;;oCAgBtB;;;;;;8CAGF,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;wDAAK;wDAAW,WAAW,KAAK,WAAW;;;;;;;;;;;;;sDAG9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6LAAC;8DAAM,KAAK,QAAQ;;;;;;;;;;;;sDAGtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;8CAIxB,6LAAC,wIAAA,CAAA,YAAS;;;;;8CAEV,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,6LAAC,oIAAA,CAAA,QAAK;oDAAW,SAAQ;oDAAU,WAAU;8DAC1C;mDADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAI;gDAAsB,KAAI;;;;;;0DAC3C,6LAAC,qIAAA,CAAA,iBAAc;0DAAC;;;;;;;;;;;;kDAIlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,6LAAC;gDAAE,WAAU;0DAAqC;;;;;;0DAGlD,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,OAAO;0DACzC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAU;;;;;;;;;;;sCAEjC,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;oCAAS,OAAO;8CAChC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5C;GAvPgB;KAAA", "debugId": null}}, {"offset": {"line": 3645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/api.ts"], "sourcesContent": ["// API client for CMS integration\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://localhost:3000'\n\nexport interface Project {\n  id: string\n  title: string\n  description: string\n  longDescription?: string\n  image?: string\n  category: string\n  technologies: string[]\n  liveUrl?: string\n  githubUrl?: string\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  image?: string\n  category: string\n  tags: string[]\n  published: boolean\n  featured: boolean\n  readTime?: number\n  views: number\n  createdAt: string\n  updatedAt: string\n  publishedAt?: string\n}\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  icon: string\n  color: string\n  bgColor: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TechStack {\n  id: string\n  name: string\n  logo: string\n  color: string\n  category: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  role: string\n  company: string\n  content: string\n  avatar?: string\n  rating: number\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\n// API functions\nexport const api = {\n  // Projects\n  getProjects: async (): Promise<Project[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch projects')\n    }\n    return response.json()\n  },\n\n  getProject: async (id: string): Promise<Project> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch project')\n    }\n    return response.json()\n  },\n\n  // Blog Posts\n  getBlogPosts: async (): Promise<BlogPost[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog posts')\n    }\n    return response.json()\n  },\n\n  getBlogPost: async (id: string): Promise<BlogPost> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/${id}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog post')\n    }\n    return response.json()\n  },\n\n  // Services\n  getServices: async (): Promise<Service[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/services`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch services')\n    }\n    return response.json()\n  },\n\n  // Tech Stack\n  getTechStack: async (): Promise<TechStack[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch tech stack')\n    }\n    return response.json()\n  },\n\n  // Testimonials\n  getTestimonials: async (): Promise<Testimonial[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch testimonials')\n    }\n    return response.json()\n  },\n}\n\n// Helper functions\nexport const getPublishedProjects = (projects: Project[]) => \n  projects.filter(project => project.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedProjects = (projects: Project[]) => \n  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)\n\nexport const getProjectsByCategory = (projects: Project[], category: string) => \n  category === 'All' \n    ? getPublishedProjects(projects)\n    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)\n\nexport const getPublishedServices = (services: Service[]) => \n  services.filter(service => service.published).sort((a, b) => a.order - b.order)\n\nexport const getTechStackByCategory = (techStack: TechStack[]) => {\n  const published = techStack.filter(tech => tech.published)\n  return published.reduce((acc, tech) => {\n    if (!acc[tech.category]) {\n      acc[tech.category] = []\n    }\n    acc[tech.category].push(tech)\n    return acc\n  }, {} as Record<string, TechStack[]>)\n}\n\nexport const getPublishedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;AACZ;AAArB,MAAM,eAAe,6DAAmC;AA+EjD,MAAM,MAAM;IACjB,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,IAAI;QACjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,IAAI;QAC7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC;QAC7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,iBAAiB;QACf,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,sBAAsB,CAAC,WAClC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE7F,MAAM,wBAAwB,CAAC,UAAqB,WACzD,aAAa,QACT,qBAAqB,YACrB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE9G,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;IACzD,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK;QAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;QACzB;QACA,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,MAAM,2BAA2B,CAAC,eACvC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAErF,MAAM,0BAA0B,CAAC,eACtC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK", "debugId": null}}, {"offset": {"line": 3739, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/related-posts.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Calendar, Clock, ArrowRight } from \"lucide-react\";\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { useState, useEffect } from \"react\";\nimport { api, type BlogPost } from \"@/lib/api\";\n\ninterface RelatedPostsProps {\n  currentPost: BlogPost;\n}\n\n// Get related posts from CMS\nconst getRelatedPosts = async (currentPost: BlogPost): Promise<BlogPost[]> => {\n  try {\n    const allPosts = await api.getBlogPosts();\n    const publishedPosts = allPosts.filter(post => post.published && post.id !== currentPost.id);\n\n    // Find posts with similar tags or category\n    const relatedPosts = publishedPosts.filter(post =>\n      post.category === currentPost.category ||\n      post.tags.some(tag => currentPost.tags.includes(tag))\n    );\n\n    // Return up to 3 related posts\n    return relatedPosts.slice(0, 3);\n  } catch (error) {\n    console.error('Error fetching related posts:', error);\n    return [];\n  }\n};\n\nexport function RelatedPosts({ currentPost }: RelatedPostsProps) {\n  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  useEffect(() => {\n    const fetchRelatedPosts = async () => {\n      try {\n        setIsLoading(true);\n        const posts = await getRelatedPosts(currentPost);\n        setRelatedPosts(posts);\n      } catch (error) {\n        console.error('Error fetching related posts:', error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchRelatedPosts();\n  }, [currentPost]);\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  if (relatedPosts.length === 0) {\n    return null;\n  }\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl font-bold mb-4\">\n            <span className=\"gradient-text\">Related Articles</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Continue your learning journey with these related articles\n          </p>\n        </motion.div>\n\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n          {relatedPosts.map((post, index) => (\n            <motion.div\n              key={post.id}\n              initial={{ opacity: 0, y: 30 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <Card className=\"h-full hover-lift group border-border/50 hover:border-border transition-all duration-300\">\n                <div className=\"relative h-40 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-t-lg\">\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"text-4xl opacity-20\">📄</div>\n                  </div>\n                  <div className=\"absolute top-3 right-3\">\n                    <Badge variant=\"secondary\" className=\"text-xs\">\n                      {post.category}\n                    </Badge>\n                  </div>\n                </div>\n                \n                <CardHeader className=\"pb-2\">\n                  <div className=\"flex items-center space-x-3 text-xs text-muted-foreground mb-2\">\n                    <div className=\"flex items-center space-x-1\">\n                      <Calendar className=\"h-3 w-3\" />\n                      <span>{formatDate(post.publishedAt || post.createdAt)}</span>\n                    </div>\n                    <div className=\"flex items-center space-x-1\">\n                      <Clock className=\"h-3 w-3\" />\n                      <span>{post.readTime}</span>\n                    </div>\n                  </div>\n                  <CardTitle className=\"text-lg group-hover:text-primary transition-colors duration-300 line-clamp-2\">\n                    {post.title}\n                  </CardTitle>\n                </CardHeader>\n                \n                <CardContent className=\"flex-1 flex flex-col\">\n                  <p className=\"text-sm text-muted-foreground mb-4 line-clamp-3 flex-1\">\n                    {post.excerpt}\n                  </p>\n                  \n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {post.tags.slice(0, 2).map((tag) => (\n                      <Badge key={tag} variant=\"outline\" className=\"text-xs\">\n                        {tag}\n                      </Badge>\n                    ))}\n                    {post.tags.length > 2 && (\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        +{post.tags.length - 2}\n                      </Badge>\n                    )}\n                  </div>\n                  \n                  <Button asChild variant=\"ghost\" className=\"group p-0 h-auto justify-start\">\n                    <Link href={`/blog/${post.slug}`}>\n                      Read Article\n                      <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                    </Link>\n                  </Button>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* More Articles CTA */}\n        <motion.div\n          className=\"text-center mt-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.4 }}\n        >\n          <Button asChild size=\"lg\" className=\"group\">\n            <Link href=\"/blog\">\n              View All Articles\n              <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAgBA,6BAA6B;AAC7B,MAAM,kBAAkB,OAAO;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,oHAAA,CAAA,MAAG,CAAC,YAAY;QACvC,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,IAAI,KAAK,EAAE,KAAK,YAAY,EAAE;QAE3F,2CAA2C;QAC3C,MAAM,eAAe,eAAe,MAAM,CAAC,CAAA,OACzC,KAAK,QAAQ,KAAK,YAAY,QAAQ,IACtC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC;QAGlD,+BAA+B;QAC/B,OAAO,aAAa,KAAK,CAAC,GAAG;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,SAAS,aAAa,EAAE,WAAW,EAAqB;;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;4DAAoB;oBACxB,IAAI;wBACF,aAAa;wBACb,MAAM,QAAQ,MAAM,gBAAgB;wBACpC,gBAAgB;oBAClB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;oBACjD,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;iCAAG;QAAC;KAAY;IAEhB,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;IACT;IAEA,qBACE,6LAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCACZ,cAAA,6LAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,6LAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAKjE,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAAsB;;;;;;;;;;;0DAEvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAY,WAAU;8DAClC,KAAK,QAAQ;;;;;;;;;;;;;;;;;kDAKpB,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,6LAAC;0EAAM,WAAW,KAAK,WAAW,IAAI,KAAK,SAAS;;;;;;;;;;;;kEAEtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,6LAAC;0EAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;0DAGxB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,KAAK,KAAK;;;;;;;;;;;;kDAIf,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;0DAGf,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,6LAAC,oIAAA,CAAA,QAAK;4DAAW,SAAQ;4DAAU,WAAU;sEAC1C;2DADS;;;;;oDAIb,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;4DAAU;4DACzC,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;0DAK3B,6LAAC,qIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,SAAQ;gDAAQ,WAAU;0DACxC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;wDAAE;sEAEhC,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtDzB,KAAK,EAAE;;;;;;;;;;8BAgElB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAExC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,OAAO;wBAAC,MAAK;wBAAK,WAAU;kCAClC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;;gCAAQ;8CAEjB,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;GAxIgB;;QAGQ,sKAAA,CAAA,YAAS;;;KAHjB", "debugId": null}}, {"offset": {"line": 4160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\n\ninterface BlogPostNavigationProps {\n  currentSlug: string;\n}\n\n// Mock navigation data - in a real app, this would come from an API\nconst getNavigationPosts = (currentSlug: string) => {\n  const posts = [\n    {\n      slug: \"building-scalable-react-applications-typescript\",\n      title: \"Building Scalable React Applications with TypeScript\"\n    },\n    {\n      slug: \"nextjs-14-app-router-complete-guide\",\n      title: \"Next.js 14 App Router: Complete Guide\"\n    },\n    {\n      slug: \"mastering-css-grid-flexbox-2024\",\n      title: \"Mastering CSS Grid and Flexbox in 2024\"\n    }\n  ];\n\n  const currentIndex = posts.findIndex(post => post.slug === currentSlug);\n  \n  return {\n    previous: currentIndex > 0 ? posts[currentIndex - 1] : null,\n    next: currentIndex < posts.length - 1 ? posts[currentIndex + 1] : null\n  };\n};\n\nexport function BlogPostNavigation({ currentSlug }: BlogPostNavigationProps) {\n  const { previous, next } = getNavigationPosts(currentSlug);\n\n  if (!previous && !next) {\n    return null;\n  }\n\n  return (\n    <motion.div\n      className=\"mt-16 pt-8 border-t border-border\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6 }}\n    >\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        {/* Previous Post */}\n        <div className=\"flex justify-start\">\n          {previous ? (\n            <Card className=\"hover-lift group border-border/50 hover:border-border transition-all duration-300 w-full\">\n              <CardContent className=\"p-6\">\n                <Button\n                  variant=\"ghost\"\n                  asChild\n                  className=\"h-auto p-0 flex flex-col items-start space-y-2 w-full\"\n                >\n                  <Link href={`/blog/${previous.slug}`}>\n                    <div className=\"flex items-center text-sm text-muted-foreground group-hover:text-primary transition-colors duration-300\">\n                      <ChevronLeft className=\"mr-1 h-4 w-4\" />\n                      Previous Article\n                    </div>\n                    <h3 className=\"text-left font-semibold group-hover:text-primary transition-colors duration-300 line-clamp-2\">\n                      {previous.title}\n                    </h3>\n                  </Link>\n                </Button>\n              </CardContent>\n            </Card>\n          ) : (\n            <div /> // Empty div to maintain grid layout\n          )}\n        </div>\n\n        {/* Next Post */}\n        <div className=\"flex justify-end\">\n          {next ? (\n            <Card className=\"hover-lift group border-border/50 hover:border-border transition-all duration-300 w-full\">\n              <CardContent className=\"p-6\">\n                <Button\n                  variant=\"ghost\"\n                  asChild\n                  className=\"h-auto p-0 flex flex-col items-end space-y-2 w-full\"\n                >\n                  <Link href={`/blog/${next.slug}`}>\n                    <div className=\"flex items-center text-sm text-muted-foreground group-hover:text-primary transition-colors duration-300\">\n                      Next Article\n                      <ChevronRight className=\"ml-1 h-4 w-4\" />\n                    </div>\n                    <h3 className=\"text-right font-semibold group-hover:text-primary transition-colors duration-300 line-clamp-2\">\n                      {next.title}\n                    </h3>\n                  </Link>\n                </Button>\n              </CardContent>\n            </Card>\n          ) : (\n            <div /> // Empty div to maintain grid layout\n          )}\n        </div>\n      </div>\n\n      {/* Back to Blog */}\n      <div className=\"text-center mt-8\">\n        <Button variant=\"outline\" asChild>\n          <Link href=\"/blog\">\n            ← Back to All Articles\n          </Link>\n        </Button>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AANA;;;;;;;AAYA,oEAAoE;AACpE,MAAM,qBAAqB,CAAC;IAC1B,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,eAAe,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAE3D,OAAO;QACL,UAAU,eAAe,IAAI,KAAK,CAAC,eAAe,EAAE,GAAG;QACvD,MAAM,eAAe,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,eAAe,EAAE,GAAG;IACpE;AACF;AAEO,SAAS,mBAAmB,EAAE,WAAW,EAA2B;IACzE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,mBAAmB;IAE9C,IAAI,CAAC,YAAY,CAAC,MAAM;QACtB,OAAO;IACT;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACZ,yBACC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,OAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,MAAM,EAAE,SAAS,IAAI,EAAE;;0DAClC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAG1C,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOzB,6LAAC;;;;iCAAO,oCAAoC;;;;;;kCAKhD,6LAAC;wBAAI,WAAU;kCACZ,qBACC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,OAAO;oCACP,WAAU;8CAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;0DAC9B,6LAAC;gDAAI,WAAU;;oDAA0G;kEAEvH,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;0DAE1B,6LAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;iDAOrB,6LAAC;;;;iCAAO,oCAAoC;;;;;;;;;;;;0BAMlD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,OAAO;8BAC/B,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCAAQ;;;;;;;;;;;;;;;;;;;;;;AAO7B;KAhFgB", "debugId": null}}]}