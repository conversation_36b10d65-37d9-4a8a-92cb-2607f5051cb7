// API client for CMS integration
const CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://localhost:3000'

export interface Project {
  id: string
  title: string
  description: string
  longDescription?: string
  image?: string
  category: string
  technologies: string[]
  liveUrl?: string
  githubUrl?: string
  featured: boolean
  published: boolean
  order: number
  createdAt: string
  updatedAt: string
}

export interface BlogPost {
  id: string
  title: string
  slug: string
  excerpt: string
  content: string
  image?: string
  category: string
  tags: string[]
  published: boolean
  featured: boolean
  readTime?: number
  views: number
  createdAt: string
  updatedAt: string
  publishedAt?: string
}

export interface Service {
  id: string
  title: string
  description: string
  features: string[]
  icon: string
  color: string
  bgColor: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface TechStack {
  id: string
  name: string
  logo: string
  color: string
  category: string
  order: number
  published: boolean
  createdAt: string
  updatedAt: string
}

export interface Testimonial {
  id: string
  name: string
  role: string
  company: string
  content: string
  avatar?: string
  rating: number
  featured: boolean
  published: boolean
  order: number
  createdAt: string
  updatedAt: string
}

// API functions
export const api = {
  // Projects
  getProjects: async (): Promise<Project[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/projects`)
    if (!response.ok) {
      throw new Error('Failed to fetch projects')
    }
    return response.json()
  },

  getProject: async (id: string): Promise<Project> => {
    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch project')
    }
    return response.json()
  },

  // Blog Posts
  getBlogPosts: async (): Promise<BlogPost[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/blog`)
    if (!response.ok) {
      throw new Error('Failed to fetch blog posts')
    }
    return response.json()
  },

  getBlogPost: async (id: string): Promise<BlogPost> => {
    const response = await fetch(`${CMS_BASE_URL}/api/blog/${id}`)
    if (!response.ok) {
      throw new Error('Failed to fetch blog post')
    }
    return response.json()
  },

  // Services
  getServices: async (): Promise<Service[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/services`)
    if (!response.ok) {
      throw new Error('Failed to fetch services')
    }
    return response.json()
  },

  // Tech Stack
  getTechStack: async (): Promise<TechStack[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)
    if (!response.ok) {
      throw new Error('Failed to fetch tech stack')
    }
    return response.json()
  },

  // Testimonials
  getTestimonials: async (): Promise<Testimonial[]> => {
    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)
    if (!response.ok) {
      throw new Error('Failed to fetch testimonials')
    }
    return response.json()
  },
}

// Helper functions
export const getPublishedProjects = (projects: Project[]) => 
  projects.filter(project => project.published).sort((a, b) => a.order - b.order)

export const getFeaturedProjects = (projects: Project[]) => 
  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)

export const getProjectsByCategory = (projects: Project[], category: string) => 
  category === 'All' 
    ? getPublishedProjects(projects)
    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)

export const getPublishedServices = (services: Service[]) => 
  services.filter(service => service.published).sort((a, b) => a.order - b.order)

export const getTechStackByCategory = (techStack: TechStack[]) => {
  const published = techStack.filter(tech => tech.published)
  return published.reduce((acc, tech) => {
    if (!acc[tech.category]) {
      acc[tech.category] = []
    }
    acc[tech.category].push(tech)
    return acc
  }, {} as Record<string, TechStack[]>)
}

export const getPublishedTestimonials = (testimonials: Testimonial[]) => 
  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)

export const getFeaturedTestimonials = (testimonials: Testimonial[]) => 
  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)
