import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug, calculateReadingTime, extractTextFromRichContent, validateSlug } from '@/lib/utils'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET(request: NextRequest) {
  try {
    const origin = request.headers.get('origin')

    const blogPosts = await prisma.blogPost.findMany({
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return withCors(NextResponse.json(blogPosts), origin)
  } catch (error) {
    console.error('Error fetching blog posts:', error)
    const origin = request.headers.get('origin')
    return withCors(NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    ), origin)
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      slug: providedSlug,
      excerpt,
      content,
      image,
      category,
      tags,
      published,
      featured,
      readTime: providedReadTime,
    } = body

    // Auto-generate slug if not provided or empty
    let finalSlug = providedSlug
    if (!finalSlug || finalSlug.trim() === '') {
      finalSlug = generateSlug(title)
    } else {
      finalSlug = validateSlug(finalSlug)
    }

    // Check for slug uniqueness
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug: finalSlug }
    })

    if (existingPost) {
      // Append timestamp to make it unique
      finalSlug = `${finalSlug}-${Date.now()}`
    }

    // Auto-calculate reading time if not provided
    let finalReadTime = providedReadTime
    if (!finalReadTime || finalReadTime <= 0) {
      const textContent = extractTextFromRichContent(content)
      finalReadTime = calculateReadingTime(textContent)
    }

    const blogPost = await prisma.blogPost.create({
      data: {
        title,
        slug: finalSlug,
        excerpt,
        content,
        image,
        category,
        tags,
        published: published || false,
        featured: featured || false,
        readTime: finalReadTime,
        publishedAt: published ? new Date() : null,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return withCors(NextResponse.json(blogPost, { status: 201 }))
  } catch (error) {
    console.error('Error creating blog post:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS(request: NextRequest) {
  const origin = request.headers.get('origin')
  return handleOptions(origin)
}
