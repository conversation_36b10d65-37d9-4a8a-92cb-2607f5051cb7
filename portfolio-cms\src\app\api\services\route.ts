import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { withCors, handleOptions } from '@/lib/cors'

export async function GET() {
  try {
    const services = await prisma.service.findMany({
      orderBy: {
        order: 'asc',
      },
    })

    return withCors(NextResponse.json(services))
  } catch (error) {
    console.error('Error fetching services:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to fetch services' },
      { status: 500 }
    ))
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      title,
      description,
      features,
      icon,
      color,
      bgColor,
      order,
      published,
    } = body

    const service = await prisma.service.create({
      data: {
        title,
        description,
        features,
        icon,
        color,
        bgColor,
        order: order || 0,
        published: published !== undefined ? published : true,
      },
    })

    return withCors(NextResponse.json(service, { status: 201 }))
  } catch (error) {
    console.error('Error creating service:', error)
    return withCors(NextResponse.json(
      { error: 'Failed to create service' },
      { status: 500 }
    ))
  }
}

export async function OPTIONS() {
  return handleOptions()
}
