{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from 'next-auth/middleware'\n\nexport default withAuth(\n  function middleware(req) {\n    // Add any additional middleware logic here\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Protect dashboard routes\n        if (req.nextUrl.pathname.startsWith('/dashboard')) {\n          return !!token\n        }\n\n        // Allow public access to API routes for portfolio website\n        if (req.nextUrl.pathname.startsWith('/api') && !req.nextUrl.pathname.startsWith('/api/auth')) {\n          // Check if request is from portfolio website or has CORS headers\n          const origin = req.headers.get('origin')\n          const referer = req.headers.get('referer')\n\n          // Allow requests from portfolio website\n          if (origin === 'http://localhost:3000' ||\n              referer?.startsWith('http://localhost:3000') ||\n              req.method === 'OPTIONS') {\n            return true\n          }\n\n          // Require authentication for CMS dashboard API access\n          return !!token\n        }\n        return true\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: ['/dashboard/:path*', '/api/:path*']\n}\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;AACrB,2CAA2C;AAC7C,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,2BAA2B;YAC3B,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe;gBACjD,OAAO,CAAC,CAAC;YACX;YAEA,0DAA0D;YAC1D,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,cAAc;gBAC5F,iEAAiE;gBACjE,MAAM,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;gBAC/B,MAAM,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC;gBAEhC,wCAAwC;gBACxC,IAAI,WAAW,2BACX,SAAS,WAAW,4BACpB,IAAI,MAAM,KAAK,WAAW;oBAC5B,OAAO;gBACT;gBAEA,sDAAsD;gBACtD,OAAO,CAAC,CAAC;YACX;YACA,OAAO;QACT;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QAAC;QAAqB;KAAc;AAC/C"}}]}