"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import Image from "next/image";
import { Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import { api, getTechStackByCategory, type TechStack } from "@/lib/api";

interface TechItem {
  id: string;
  name: string;
  logo: string;
  color: string;
  category: string;
}

const TechRow = ({
  techs,
  direction = "left",
  speed = 30
}: {
  techs: TechItem[];
  direction?: "left" | "right";
  speed?: number;
}) => {
  return (
    <div className="flex overflow-hidden">
      <motion.div
        className="flex space-x-8 whitespace-nowrap"
        animate={{
          x: direction === "left" ? [0, -1000] : [-1000, 0],
        }}
        transition={{
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: speed,
            ease: "linear",
          },
        }}
      >
        {/* Duplicate the array to create seamless loop */}
        {[...techs, ...techs].map((tech, index) => (
          <motion.div
            key={`${tech.id}-${index}`}
            className="flex items-center space-x-3 bg-card border border-border rounded-lg px-6 py-3 shadow-sm hover:shadow-md transition-shadow duration-200"
            whileHover={{ scale: 1.05 }}
          >
            <div className="w-8 h-8 relative">
              <Image
                src={tech.logo}
                alt={tech.name}
                fill
                className="object-contain"
              />
            </div>
            <span className={`font-medium ${tech.color}`}>{tech.name}</span>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export function TechStack() {
  const [techStackByCategory, setTechStackByCategory] = useState<Record<string, TechStack[]>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Fetch tech stack from CMS
  useEffect(() => {
    const fetchTechStack = async () => {
      try {
        setIsLoading(true);
        const data = await api.getTechStack();
        setTechStackByCategory(getTechStackByCategory(data));
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch tech stack');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTechStack();
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
              <span className="gradient-text">Technologies I Love</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Loading technologies...
            </p>
          </div>
          <div className="flex justify-center">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </section>
    );
  }

  // Error state
  if (error) {
    return (
      <section className="py-20 bg-muted/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
              <span className="gradient-text">Technologies I Love</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Unable to load technologies. Please try again later.
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section ref={ref} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Technologies I Love</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            I'm passionate about cutting-edge technologies and constantly learning new tools
            to build amazing digital experiences.
          </p>
        </motion.div>

        <motion.div
          className="space-y-8"
          initial={{ opacity: 0 }}
          animate={inView ? { opacity: 1 } : {}}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          {Object.entries(techStackByCategory).map(([category, techs], index) => (
            <div key={category} className="space-y-4">
              <h3 className="text-xl font-semibold text-center text-muted-foreground capitalize">
                {category}
              </h3>
              <TechRow
                techs={techs}
                direction={index % 2 === 0 ? "left" : "right"}
                speed={25 + index * 5}
              />
            </div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-20"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          {[
            { number: "3+", label: "Years Experience" },
            { number: "50+", label: "Projects Completed" },
            { number: "20+", label: "Happy Clients" },
            { number: "99%", label: "Client Satisfaction" },
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              className="text-center"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={inView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
            >
              <div className="text-3xl sm:text-4xl font-bold gradient-text-blue mb-2">
                {stat.number}
              </div>
              <div className="text-sm text-muted-foreground">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
