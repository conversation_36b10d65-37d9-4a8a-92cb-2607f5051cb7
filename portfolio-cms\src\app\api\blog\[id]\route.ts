import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug, calculateReadingTime, extractTextFromRichContent, validateSlug } from '@/lib/utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const blogPost = await prisma.blogPost.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    if (!blogPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(blogPost)
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()
    const {
      title,
      slug: providedSlug,
      excerpt,
      content,
      image,
      category,
      tags,
      published,
      featured,
      readTime: providedReadTime,
    } = body

    // Get current post to check if slug changed
    const currentPost = await prisma.blogPost.findUnique({
      where: { id }
    })

    if (!currentPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      )
    }

    // Handle slug validation and uniqueness
    let finalSlug = providedSlug
    if (!finalSlug || finalSlug.trim() === '') {
      finalSlug = generateSlug(title)
    } else {
      finalSlug = validateSlug(finalSlug)
    }

    // Check for slug uniqueness only if slug changed
    if (finalSlug !== currentPost.slug) {
      const existingPost = await prisma.blogPost.findUnique({
        where: { slug: finalSlug }
      })

      if (existingPost) {
        return NextResponse.json(
          { error: 'A blog post with this slug already exists' },
          { status: 400 }
        )
      }
    }

    // Auto-calculate reading time if not provided or content changed
    let finalReadTime = providedReadTime
    if (!finalReadTime || finalReadTime <= 0 || content !== currentPost.content) {
      const textContent = extractTextFromRichContent(content)
      finalReadTime = calculateReadingTime(textContent)
    }

    const blogPost = await prisma.blogPost.update({
      where: { id },
      data: {
        title,
        slug: finalSlug,
        excerpt,
        content,
        image,
        category,
        tags,
        published,
        featured,
        readTime: finalReadTime,
        publishedAt: published ? new Date() : null,
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    return NextResponse.json(blogPost)
  } catch (error) {
    console.error('Error updating blog post:', error)
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    await prisma.blogPost.delete({
      where: { id },
    })

    return NextResponse.json({ message: 'Blog post deleted successfully' })
  } catch (error) {
    console.error('Error deleting blog post:', error)
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    )
  }
}
