{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_717c1e95._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_ac7adf20.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "GmR490q78K+bBIm3nmrLlRQO8rHHbnjcdhaBgWmTT64=", "__NEXT_PREVIEW_MODE_ID": "69f97bb58dd80d3a2d964a40dd826395", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a85361ab1565c602f414f4ea71331eea9c9d6b23d85ae9a4e32d3bff8bbe13e6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a9ebc28fb1e196674a8d2c8ec2bd23f05f8b7a7f19866096cb3cc4b50ba2b83f"}}}, "sortedMiddleware": ["/"], "functions": {}}