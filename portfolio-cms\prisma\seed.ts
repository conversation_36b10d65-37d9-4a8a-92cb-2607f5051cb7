import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: hashedPassword,
      role: 'ADMIN',
    },
  })

  console.log('Created admin user:', adminUser)

  // Create sample services
  const services = [
    {
      title: 'Full Stack Development',
      description: 'End-to-end web application development using modern technologies like React, Next.js, Node.js, and TypeScript.',
      features: [
        'Custom Web Applications',
        'API Development',
        'Database Design',
        'Performance Optimization'
      ],
      icon: 'Code',
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
      order: 1,
    },
    {
      title: 'Frontend Development',
      description: 'Creating responsive and interactive user interfaces with modern frameworks and best practices.',
      features: [
        'React & Next.js Development',
        'Responsive Design',
        'Component Libraries',
        'State Management'
      ],
      icon: 'Monitor',
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
      order: 2,
    },
    {
      title: 'Backend Development',
      description: 'Building robust server-side applications with scalable architecture and secure APIs.',
      features: [
        'RESTful APIs',
        'Database Design',
        'Authentication & Authorization',
        'Cloud Integration'
      ],
      icon: 'Server',
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10',
      order: 3,
    },
  ]

  for (const service of services) {
    await prisma.service.create({
      data: service,
    })
  }

  console.log('Created services')

  // Create sample tech stack
  const techStacks = [
    // Frontend
    { name: 'React', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg', color: 'text-blue-500', category: 'frontend', order: 1 },
    { name: 'Next.js', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg', color: 'text-black dark:text-white', category: 'frontend', order: 2 },
    { name: 'TypeScript', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg', color: 'text-blue-600', category: 'frontend', order: 3 },
    { name: 'Tailwind CSS', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tailwindcss/tailwindcss-plain.svg', color: 'text-cyan-500', category: 'frontend', order: 4 },

    // Backend
    { name: 'Node.js', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg', color: 'text-green-500', category: 'backend', order: 1 },
    { name: 'PostgreSQL', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg', color: 'text-blue-700', category: 'backend', order: 2 },
    { name: 'Prisma', logo: 'https://www.prisma.io/images/favicon-32x32.png', color: 'text-indigo-500', category: 'backend', order: 3 },
    { name: 'MongoDB', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg', color: 'text-green-600', category: 'backend', order: 4 },

    // Tools
    { name: 'Git', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/git/git-original.svg', color: 'text-orange-500', category: 'tools', order: 1 },
    { name: 'Docker', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg', color: 'text-blue-500', category: 'tools', order: 2 },
    { name: 'AWS', logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg', color: 'text-yellow-500', category: 'tools', order: 3 },
    { name: 'Vercel', logo: 'https://assets.vercel.com/image/upload/v1662130559/nextjs/Icon_light_background.png', color: 'text-black dark:text-white', category: 'tools', order: 4 },
  ]

  for (const tech of techStacks) {
    await prisma.techStack.create({
      data: tech,
    })
  }

  console.log('Created tech stack')

  // Create sample projects
  const projects = [
    {
      title: 'E-Commerce Platform',
      description: 'A modern e-commerce platform built with Next.js, featuring real-time inventory, payment processing, and admin dashboard.',
      longDescription: 'This comprehensive e-commerce solution features a modern React frontend with Next.js for optimal performance, a robust Node.js backend with PostgreSQL database, and integrated payment processing through Stripe.',
      category: 'Full Stack',
      technologies: ['Next.js', 'TypeScript', 'Prisma', 'PostgreSQL', 'Stripe'],
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/ashishkamat/ecommerce',
      featured: true,
      authorId: adminUser.id,
      order: 1,
    },
    {
      title: 'Task Management App',
      description: 'A collaborative task management application with real-time updates, team collaboration, and project tracking.',
      longDescription: 'Built with React and Node.js, this application provides comprehensive project management features including task assignment, progress tracking, and team collaboration tools.',
      category: 'Full Stack',
      technologies: ['React', 'Node.js', 'Socket.io', 'MongoDB', 'Express'],
      liveUrl: 'https://example.com',
      githubUrl: 'https://github.com/ashishkamat/task-manager',
      featured: true,
      authorId: adminUser.id,
      order: 2,
    },
  ]

  for (const project of projects) {
    await prisma.project.create({
      data: project,
    })
  }

  console.log('Created projects')

  // Create sample testimonials
  const testimonials = [
    {
      name: 'John Smith',
      role: 'CTO',
      company: 'Tech Solutions Inc.',
      content: 'Ashish delivered an exceptional web application that exceeded our expectations. His attention to detail and technical expertise are outstanding.',
      rating: 5,
      featured: true,
      authorId: adminUser.id,
      order: 1,
    },
    {
      name: 'Sarah Johnson',
      role: 'Product Manager',
      company: 'Digital Innovations',
      content: 'Working with Ashish was a pleasure. He understood our requirements perfectly and delivered a high-quality solution on time.',
      rating: 5,
      featured: true,
      authorId: adminUser.id,
      order: 2,
    },
  ]

  for (const testimonial of testimonials) {
    await prisma.testimonial.create({
      data: testimonial,
    })
  }

  console.log('Created testimonials')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })
