'use client'

import { useEffect, useState } from 'react'
import { DashboardLayout } from '@/components/dashboard/layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  BarChart3,
  <PERSON>,
  Eye,
  Clock,
  TrendingUp,
  TrendingDown,
} from 'lucide-react'

interface AnalyticsData {
  pageViews: number
  sessions: number
  users: number
  bounceRate: number
  avgDuration: number
  topPages: Array<{ page: string; views: number }>
  topSources: Array<{ source: string; sessions: number }>
}

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate analytics data for now
    setTimeout(() => {
      setAnalytics({
        pageViews: 12543,
        sessions: 8921,
        users: 6789,
        bounceRate: 42.3,
        avgDuration: 185,
        topPages: [
          { page: '/', views: 4521 },
          { page: '/projects', views: 2134 },
          { page: '/about', views: 1876 },
          { page: '/blog', views: 1543 },
          { page: '/contact', views: 987 },
        ],
        topSources: [
          { source: 'Direct', sessions: 3456 },
          { source: 'Google', sessions: 2890 },
          { source: 'LinkedIn', sessions: 1234 },
          { source: 'GitHub', sessions: 987 },
          { source: 'Twitter', sessions: 354 },
        ],
      })
      setLoading(false)
    }, 1000)
  }, [])

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Portfolio website performance and visitor insights
          </p>
        </div>

        {loading ? (
          <div className="text-center py-8">Loading analytics...</div>
        ) : analytics ? (
          <>
            {/* Key Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Page Views</CardTitle>
                  <Eye className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.pageViews.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    +12.5% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Sessions</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.sessions.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    +8.2% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.users.toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    +15.3% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Duration</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatDuration(analytics.avgDuration)}</div>
                  <p className="text-xs text-muted-foreground">
                    <TrendingDown className="inline h-3 w-3 mr-1" />
                    -2.1% from last month
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Top Pages and Sources */}
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Top Pages</CardTitle>
                  <CardDescription>Most visited pages on your portfolio</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.topPages.map((page, index) => (
                      <div key={page.page} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                            {index + 1}
                          </Badge>
                          <span className="font-medium">{page.page}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {page.views.toLocaleString()} views
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Traffic Sources</CardTitle>
                  <CardDescription>Where your visitors are coming from</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.topSources.map((source, index) => (
                      <div key={source.source} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
                            {index + 1}
                          </Badge>
                          <span className="font-medium">{source.source}</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {source.sessions.toLocaleString()} sessions
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Additional Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Additional Metrics</CardTitle>
                <CardDescription>More insights about your portfolio performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{analytics.bounceRate}%</div>
                    <p className="text-sm text-muted-foreground">Bounce Rate</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {(analytics.pageViews / analytics.sessions).toFixed(1)}
                    </div>
                    <p className="text-sm text-muted-foreground">Pages per Session</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {((analytics.users / analytics.sessions) * 100).toFixed(1)}%
                    </div>
                    <p className="text-sm text-muted-foreground">New Visitors</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">No analytics data available</p>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
