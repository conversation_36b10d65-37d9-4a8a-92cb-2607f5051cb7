import { DashboardLayout } from '@/components/dashboard/layout'
import { ProjectForm } from '@/components/forms/project-form'

interface EditProjectPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function EditProjectPage({ params }: EditProjectPageProps) {
  const { id } = await params
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Project</h1>
          <p className="text-muted-foreground">
            Update your project information
          </p>
        </div>

        <ProjectForm projectId={id} />
      </div>
    </DashboardLayout>
  )
}
