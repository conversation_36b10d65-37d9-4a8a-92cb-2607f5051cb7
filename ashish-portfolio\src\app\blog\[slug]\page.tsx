import { Metadata } from "next";
import { notFound } from "next/navigation";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { BlogPostHeader } from "@/components/blog/blog-post-header";
import { BlogPostContent } from "@/components/blog/blog-post-content";
import { BlogPostSidebar } from "@/components/blog/blog-post-sidebar";
import { RelatedPosts } from "@/components/blog/related-posts";
import { BlogPostNavigation } from "@/components/blog/blog-post-navigation";
import { api } from "@/lib/api";

// Get blog post from CMS
const getBlogPost = async (slug: string) => {
  try {
    const blogPosts = await api.getBlogPosts();
    return blogPosts.find(post => post.slug === slug && post.published);
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return null;
  }
};

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    return {
      title: "Post Not Found",
    };
  }

  return {
    title: `${post.title} - Ashish Kamat`,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.publishedAt,
      authors: ["Ashish Kamat"],
      tags: post.tags,
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
    },
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  const post = await getBlogPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="min-h-screen">
      <Navigation />
      <main className="pt-16">
        <article>
          <BlogPostHeader post={post} />
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div className="grid lg:grid-cols-4 gap-12">
              <div className="lg:col-span-3">
                <BlogPostContent post={post} />
                <BlogPostNavigation currentSlug={slug} />
              </div>
              <div className="lg:col-span-1">
                <BlogPostSidebar post={post} />
              </div>
            </div>
          </div>
        </article>
        <RelatedPosts currentPost={post} />
      </main>
      <Footer />
    </div>
  );
}
