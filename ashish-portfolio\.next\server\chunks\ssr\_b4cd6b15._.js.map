{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx <module evaluation>\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/navigation.tsx\",\n    \"Navigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx <module evaluation>\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Footer = registerClientReference(\n    function() { throw new Error(\"Attempted to call Footer() from the server but Foot<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/footer.tsx\",\n    \"Footer\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostHeader() from the server but BlogPostHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-header.tsx <module evaluation>\",\n    \"BlogPostHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0EACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostHeader() from the server but BlogPostHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-header.tsx\",\n    \"BlogPostHeader\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,sDACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostContent() from the server but BlogPostContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-content.tsx <module evaluation>\",\n    \"BlogPostContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2EACA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-content.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostContent() from the server but BlogPostContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-content.tsx\",\n    \"BlogPostContent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uDACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostSidebar() from the server but BlogPostSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-sidebar.tsx <module evaluation>\",\n    \"BlogPostSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2EACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-sidebar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostSidebar() from the server but BlogPostSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-sidebar.tsx\",\n    \"BlogPostSidebar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,uDACA", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/related-posts.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RelatedPosts = registerClientReference(\n    function() { throw new Error(\"Attempted to call RelatedPosts() from the server but RelatedPosts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/related-posts.tsx <module evaluation>\",\n    \"RelatedPosts\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uEACA", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/related-posts.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const RelatedPosts = registerClientReference(\n    function() { throw new Error(\"Attempted to call RelatedPosts() from the server but RelatedPosts is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/related-posts.tsx\",\n    \"RelatedPosts\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,mDACA", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostNavigation() from the server but BlogPostNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-navigation.tsx <module evaluation>\",\n    \"BlogPostNavigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-post-navigation.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const BlogPostNavigation = registerClientReference(\n    function() { throw new Error(\"Attempted to call BlogPostNavigation() from the server but BlogPostNavigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/blog/blog-post-navigation.tsx\",\n    \"BlogPostNavigation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,qBAAqB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/api.ts"], "sourcesContent": ["// API client for CMS integration\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://localhost:3000'\n\nexport interface Project {\n  id: string\n  title: string\n  description: string\n  longDescription?: string\n  image?: string\n  category: string\n  technologies: string[]\n  liveUrl?: string\n  githubUrl?: string\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  image?: string\n  category: string\n  tags: string[]\n  published: boolean\n  featured: boolean\n  readTime?: number\n  views: number\n  createdAt: string\n  updatedAt: string\n  publishedAt?: string\n}\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  icon: string\n  color: string\n  bgColor: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TechStack {\n  id: string\n  name: string\n  logo: string\n  color: string\n  category: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  role: string\n  company: string\n  content: string\n  avatar?: string\n  rating: number\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\n// API functions\nexport const api = {\n  // Projects\n  getProjects: async (): Promise<Project[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch projects')\n    }\n    return response.json()\n  },\n\n  getProject: async (id: string): Promise<Project> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch project')\n    }\n    return response.json()\n  },\n\n  // Blog Posts\n  getBlogPosts: async (): Promise<BlogPost[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog posts')\n    }\n    return response.json()\n  },\n\n  getBlogPost: async (slug: string): Promise<BlogPost> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/${slug}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog post')\n    }\n    return response.json()\n  },\n\n  // Services\n  getServices: async (): Promise<Service[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/services`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch services')\n    }\n    return response.json()\n  },\n\n  // Tech Stack\n  getTechStack: async (): Promise<TechStack[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch tech stack')\n    }\n    return response.json()\n  },\n\n  // Testimonials\n  getTestimonials: async (): Promise<Testimonial[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch testimonials')\n    }\n    return response.json()\n  },\n}\n\n// Helper functions\nexport const getPublishedProjects = (projects: Project[]) => \n  projects.filter(project => project.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedProjects = (projects: Project[]) => \n  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)\n\nexport const getProjectsByCategory = (projects: Project[], category: string) => \n  category === 'All' \n    ? getPublishedProjects(projects)\n    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)\n\nexport const getPublishedServices = (services: Service[]) => \n  services.filter(service => service.published).sort((a, b) => a.order - b.order)\n\nexport const getTechStackByCategory = (techStack: TechStack[]) => {\n  const published = techStack.filter(tech => tech.published)\n  return published.reduce((acc, tech) => {\n    if (!acc[tech.category]) {\n      acc[tech.category] = []\n    }\n    acc[tech.category].push(tech)\n    return acc\n  }, {} as Record<string, TechStack[]>)\n}\n\nexport const getPublishedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;AACjC,MAAM,eAAe,6DAAmC;AA+EjD,MAAM,MAAM;IACjB,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,IAAI;QACjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,MAAM;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC;QAC7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,iBAAiB;QACf,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,sBAAsB,CAAC,WAClC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE7F,MAAM,wBAAwB,CAAC,UAAqB,WACzD,aAAa,QACT,qBAAqB,YACrB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE9G,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;IACzD,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK;QAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;QACzB;QACA,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,MAAM,2BAA2B,CAAC,eACvC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAErF,MAAM,0BAA0B,CAAC,eACtC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK", "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/app/blog/%5Bslug%5D/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\nimport { notFound } from \"next/navigation\";\nimport { Navigation } from \"@/components/navigation\";\nimport { Footer } from \"@/components/footer\";\nimport { BlogPostHeader } from \"@/components/blog/blog-post-header\";\nimport { BlogPostContent } from \"@/components/blog/blog-post-content\";\nimport { BlogPostSidebar } from \"@/components/blog/blog-post-sidebar\";\nimport { RelatedPosts } from \"@/components/blog/related-posts\";\nimport { BlogPostNavigation } from \"@/components/blog/blog-post-navigation\";\nimport { api } from \"@/lib/api\";\n\n// Get blog post from CMS\nconst getBlogPost = async (slug: string) => {\n  try {\n    const blogPosts = await api.getBlogPosts();\n    return blogPosts.find(post => post.slug === slug && post.published);\n  } catch (error) {\n    console.error('Error fetching blog post:', error);\n    return null;\n  }\n};\n\ninterface BlogPostPageProps {\n  params: {\n    slug: string;\n  };\n}\n\nexport async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {\n  const { slug } = await params;\n  const post = await getBlogPost(slug);\n\n  if (!post) {\n    return {\n      title: \"Post Not Found\",\n    };\n  }\n\n  return {\n    title: `${post.title} - Ashish Kamat`,\n    description: post.excerpt,\n    openGraph: {\n      title: post.title,\n      description: post.excerpt,\n      type: \"article\",\n      publishedTime: post.publishedAt,\n      authors: [\"Ashish Kamat\"],\n      tags: post.tags,\n    },\n    twitter: {\n      card: \"summary_large_image\",\n      title: post.title,\n      description: post.excerpt,\n    },\n  };\n}\n\nexport default async function BlogPostPage({ params }: BlogPostPageProps) {\n  const { slug } = await params;\n  const post = await getBlogPost(slug);\n\n  if (!post) {\n    notFound();\n  }\n\n  return (\n    <div className=\"min-h-screen\">\n      <Navigation />\n      <main className=\"pt-16\">\n        <article>\n          <BlogPostHeader post={post} />\n          <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n            <div className=\"grid lg:grid-cols-4 gap-12\">\n              <div className=\"lg:col-span-3\">\n                <BlogPostContent post={post} />\n                <BlogPostNavigation currentSlug={slug} />\n              </div>\n              <div className=\"lg:col-span-1\">\n                <BlogPostSidebar post={post} />\n              </div>\n            </div>\n          </div>\n        </article>\n        <RelatedPosts currentPost={post} />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEA,yBAAyB;AACzB,MAAM,cAAc,OAAO;IACzB,IAAI;QACF,MAAM,YAAY,MAAM,iHAAA,CAAA,MAAG,CAAC,YAAY;QACxC,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,SAAS;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAQO,eAAe,iBAAiB,EAAE,MAAM,EAAqB;IAClE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,MAAM;QACT,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;QACL,OAAO,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC;QACrC,aAAa,KAAK,OAAO;QACzB,WAAW;YACT,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;YACzB,MAAM;YACN,eAAe,KAAK,WAAW;YAC/B,SAAS;gBAAC;aAAe;YACzB,MAAM,KAAK,IAAI;QACjB;QACA,SAAS;YACP,MAAM;YACN,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,OAAO;QAC3B;IACF;AACF;AAEe,eAAe,aAAa,EAAE,MAAM,EAAqB;IACtE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;IACvB,MAAM,OAAO,MAAM,YAAY;IAE/B,IAAI,CAAC,MAAM;QACT,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;;0CACC,8OAAC,oJAAA,CAAA,iBAAc;gCAAC,MAAM;;;;;;0CACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,qJAAA,CAAA,kBAAe;oDAAC,MAAM;;;;;;8DACvB,8OAAC,wJAAA,CAAA,qBAAkB;oDAAC,aAAa;;;;;;;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,qJAAA,CAAA,kBAAe;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAK/B,8OAAC,8IAAA,CAAA,eAAY;wBAAC,aAAa;;;;;;;;;;;;0BAE7B,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;AAGb", "debugId": null}}]}