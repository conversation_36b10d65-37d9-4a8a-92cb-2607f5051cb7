"use client";

import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

const skillCategories = [
  {
    title: "Frontend Development",
    skills: [
      { name: "React/Next.js", level: 95, color: "bg-blue-500" },
      { name: "TypeScript", level: 90, color: "bg-blue-600" },
      { name: "Tailwind CSS", level: 92, color: "bg-cyan-500" },
      { name: "JavaScript (ES6+)", level: 88, color: "bg-yellow-500" },
      { name: "HTML5 & CSS3", level: 95, color: "bg-orange-500" },
      { name: "Framer Motion", level: 85, color: "bg-purple-500" },
    ]
  },
  {
    title: "Backend Development",
    skills: [
      { name: "Node.js", level: 88, color: "bg-green-600" },
      { name: "Express.js", level: 85, color: "bg-gray-600" },
      { name: "PostgreSQL", level: 82, color: "bg-blue-800" },
      { name: "MongoDB", level: 80, color: "bg-green-500" },
      { name: "Prisma ORM", level: 85, color: "bg-indigo-600" },
      { name: "REST APIs", level: 90, color: "bg-teal-500" },
    ]
  },
  {
    title: "Tools & Technologies",
    skills: [
      { name: "Git & GitHub", level: 92, color: "bg-gray-800" },
      { name: "Docker", level: 75, color: "bg-blue-500" },
      { name: "AWS", level: 70, color: "bg-orange-500" },
      { name: "Vercel", level: 88, color: "bg-black" },
      { name: "Figma", level: 85, color: "bg-purple-500" },
      { name: "VS Code", level: 95, color: "bg-blue-600" },
    ]
  },
  {
    title: "Soft Skills",
    skills: [
      { name: "Problem Solving", level: 92, color: "bg-emerald-500" },
      { name: "Team Leadership", level: 85, color: "bg-rose-500" },
      { name: "Communication", level: 88, color: "bg-amber-500" },
      { name: "Project Management", level: 80, color: "bg-violet-500" },
      { name: "Mentoring", level: 82, color: "bg-pink-500" },
      { name: "Adaptability", level: 90, color: "bg-sky-500" },
    ]
  }
];

export function Skills() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <section ref={ref} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6">
            <span className="gradient-text">Skills & Expertise</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            A comprehensive overview of my technical skills and proficiency levels across different technologies and domains.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8">
          {skillCategories.map((category, categoryIndex) => (
            <motion.div
              key={category.title}
              initial={{ opacity: 0, y: 50 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: categoryIndex * 0.1 }}
            >
              <Card className="h-full hover-lift border-border/50 hover:border-border transition-all duration-300">
                <CardHeader>
                  <CardTitle className="text-xl font-bold">{category.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {category.skills.map((skill, skillIndex) => (
                      <motion.div
                        key={skill.name}
                        className="space-y-2"
                        initial={{ opacity: 0, x: -20 }}
                        animate={inView ? { opacity: 1, x: 0 } : {}}
                        transition={{ 
                          duration: 0.5, 
                          delay: categoryIndex * 0.1 + skillIndex * 0.05 
                        }}
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-medium text-sm">{skill.name}</span>
                          <span className="text-sm text-muted-foreground">{skill.level}%</span>
                        </div>
                        <div className="relative">
                          <div className="w-full bg-muted rounded-full h-2">
                            <motion.div
                              className={`h-2 rounded-full ${skill.color}`}
                              initial={{ width: 0 }}
                              animate={inView ? { width: `${skill.level}%` } : { width: 0 }}
                              transition={{ 
                                duration: 1, 
                                delay: categoryIndex * 0.1 + skillIndex * 0.05 + 0.2,
                                ease: "easeOut"
                              }}
                            />
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Skills Summary */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl p-8 border border-border/50">
            <h3 className="text-2xl font-bold mb-4">Continuous Learning</h3>
            <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
              I'm passionate about staying up-to-date with the latest technologies and best practices. 
              Currently exploring AI/ML integration in web applications and advanced React patterns.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              {["AI/ML", "Web3", "GraphQL", "Microservices", "DevOps", "Mobile Development"].map((skill) => (
                <span 
                  key={skill}
                  className="px-3 py-1 bg-background border border-border rounded-full text-sm font-medium"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
