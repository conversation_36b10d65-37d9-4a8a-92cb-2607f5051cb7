import { <PERSON>ada<PERSON> } from "next";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { ProjectsHero } from "@/components/projects/projects-hero";
import { ProjectsGrid } from "@/components/projects/projects-grid";
import { ProjectsStats } from "@/components/projects/projects-stats";

export const metadata: Metadata = {
  title: "Projects - Ashish Kamat",
  description: "Explore my portfolio of web development projects, featuring modern applications built with React, Next.js, and cutting-edge technologies.",
  openGraph: {
    title: "Projects - Ashish Kamat",
    description: "Explore my portfolio of web development projects, featuring modern applications built with React, Next.js, and cutting-edge technologies.",
  },
};

export default function ProjectsPage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main className="pt-16">
        <ProjectsHero />
        <ProjectsStats />
        <ProjectsGrid />
      </main>
      <Footer />
    </div>
  );
}
