import { <PERSON>ada<PERSON> } from "next";
import { Navigation } from "@/components/navigation";
import { Footer } from "@/components/footer";
import { ContactSection } from "@/components/contact-section";

export const metadata: Metadata = {
  title: "Contact - Ash<PERSON>mat",
  description: "Get in touch with <PERSON><PERSON> for web development projects, collaborations, or just to say hello. Available for freelance work and consulting.",
  openGraph: {
    title: "Contact - Ashish Kamat",
    description: "Get in touch with <PERSON><PERSON> for web development projects, collaborations, or just to say hello. Available for freelance work and consulting.",
  },
};

export default function ContactPage() {
  return (
    <div className="min-h-screen">
      <Navigation />
      <main className="pt-16">
        <ContactSection />
      </main>
      <Footer />
    </div>
  );
}
