{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/command-palette.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { \n  Search, \n  Home, \n  User, \n  Briefcase, \n  Mail, \n  FileText,\n  ExternalLink,\n  Github,\n  Linkedin,\n  Twitter\n} from \"lucide-react\";\nimport {\n  <PERSON><PERSON>,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n} from \"@/components/ui/dialog\";\nimport { Input } from \"@/components/ui/input\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface Command {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  action: () => void;\n  category: string;\n  keywords: string[];\n}\n\ninterface CommandPaletteProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n}\n\nexport function CommandPalette({ open, onOpenChange }: CommandPaletteProps) {\n  const [search, setSearch] = useState(\"\");\n  const [selectedIndex, setSelectedIndex] = useState(0);\n  const router = useRouter();\n\n  const commands: Command[] = [\n    // Navigation\n    {\n      id: \"home\",\n      title: \"Home\",\n      description: \"Go to homepage\",\n      icon: Home,\n      action: () => router.push(\"/\"),\n      category: \"Navigation\",\n      keywords: [\"home\", \"main\", \"landing\"],\n    },\n    {\n      id: \"about\",\n      title: \"About\",\n      description: \"Learn more about me\",\n      icon: User,\n      action: () => router.push(\"/about\"),\n      category: \"Navigation\",\n      keywords: [\"about\", \"bio\", \"experience\", \"skills\"],\n    },\n    {\n      id: \"projects\",\n      title: \"Projects\",\n      description: \"View my work and projects\",\n      icon: Briefcase,\n      action: () => router.push(\"/projects\"),\n      category: \"Navigation\",\n      keywords: [\"projects\", \"work\", \"portfolio\", \"showcase\"],\n    },\n    {\n      id: \"blog\",\n      title: \"Blog\",\n      description: \"Read my latest articles\",\n      icon: FileText,\n      action: () => router.push(\"/blog\"),\n      category: \"Navigation\",\n      keywords: [\"blog\", \"articles\", \"writing\", \"posts\"],\n    },\n    {\n      id: \"contact\",\n      title: \"Contact\",\n      description: \"Get in touch with me\",\n      icon: Mail,\n      action: () => router.push(\"/contact\"),\n      category: \"Navigation\",\n      keywords: [\"contact\", \"email\", \"message\", \"hire\"],\n    },\n    // External Links\n    {\n      id: \"github\",\n      title: \"GitHub\",\n      description: \"View my GitHub profile\",\n      icon: Github,\n      action: () => window.open(\"https://github.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"github\", \"code\", \"repositories\", \"open source\"],\n    },\n    {\n      id: \"linkedin\",\n      title: \"LinkedIn\",\n      description: \"Connect with me on LinkedIn\",\n      icon: Linkedin,\n      action: () => window.open(\"https://linkedin.com/in/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"linkedin\", \"professional\", \"network\", \"career\"],\n    },\n    {\n      id: \"twitter\",\n      title: \"Twitter\",\n      description: \"Follow me on Twitter\",\n      icon: Twitter,\n      action: () => window.open(\"https://twitter.com/ashishkamat\", \"_blank\"),\n      category: \"Social\",\n      keywords: [\"twitter\", \"social\", \"updates\", \"thoughts\"],\n    },\n    // Quick Actions\n    {\n      id: \"email\",\n      title: \"Send Email\",\n      description: \"Send me an email directly\",\n      icon: Mail,\n      action: () => window.open(\"mailto:<EMAIL>\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"email\", \"contact\", \"message\", \"hire\"],\n    },\n    {\n      id: \"resume\",\n      title: \"Download Resume\",\n      description: \"Download my latest resume\",\n      icon: ExternalLink,\n      action: () => window.open(\"/resume.pdf\", \"_blank\"),\n      category: \"Quick Actions\",\n      keywords: [\"resume\", \"cv\", \"download\", \"hire\"],\n    },\n  ];\n\n  const filteredCommands = commands.filter((command) => {\n    const searchLower = search.toLowerCase();\n    return (\n      command.title.toLowerCase().includes(searchLower) ||\n      command.description.toLowerCase().includes(searchLower) ||\n      command.keywords.some((keyword) => keyword.includes(searchLower))\n    );\n  });\n\n  const groupedCommands = filteredCommands.reduce((acc, command) => {\n    if (!acc[command.category]) {\n      acc[command.category] = [];\n    }\n    acc[command.category].push(command);\n    return acc;\n  }, {} as Record<string, Command[]>);\n\n  useEffect(() => {\n    setSelectedIndex(0);\n  }, [search]);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if (!open) return;\n\n      if (e.key === \"ArrowDown\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev < filteredCommands.length - 1 ? prev + 1 : 0\n        );\n      } else if (e.key === \"ArrowUp\") {\n        e.preventDefault();\n        setSelectedIndex((prev) => \n          prev > 0 ? prev - 1 : filteredCommands.length - 1\n        );\n      } else if (e.key === \"Enter\") {\n        e.preventDefault();\n        if (filteredCommands[selectedIndex]) {\n          filteredCommands[selectedIndex].action();\n          onOpenChange(false);\n          setSearch(\"\");\n        }\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, [open, selectedIndex, filteredCommands, onOpenChange]);\n\n  const handleCommandSelect = (command: Command) => {\n    command.action();\n    onOpenChange(false);\n    setSearch(\"\");\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"max-w-2xl p-0 overflow-hidden\">\n        <DialogHeader className=\"p-4 pb-0\">\n          <DialogTitle className=\"sr-only\">Command Palette</DialogTitle>\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Type a command or search...\"\n              value={search}\n              onChange={(e) => setSearch(e.target.value)}\n              className=\"pl-10 border-0 focus-visible:ring-0 text-base\"\n              autoFocus\n            />\n          </div>\n        </DialogHeader>\n\n        <div className=\"max-h-96 overflow-y-auto p-4 pt-0\">\n          {Object.keys(groupedCommands).length === 0 ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              No commands found for \"{search}\"\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {Object.entries(groupedCommands).map(([category, commands]) => (\n                <div key={category}>\n                  <div className=\"text-xs font-medium text-muted-foreground uppercase tracking-wider mb-2 px-2\">\n                    {category}\n                  </div>\n                  <div className=\"space-y-1\">\n                    {commands.map((command, index) => {\n                      const globalIndex = filteredCommands.indexOf(command);\n                      const Icon = command.icon;\n                      \n                      return (\n                        <motion.button\n                          key={command.id}\n                          onClick={() => handleCommandSelect(command)}\n                          className={`w-full text-left p-3 rounded-lg transition-colors duration-200 flex items-center space-x-3 ${\n                            globalIndex === selectedIndex\n                              ? \"bg-accent text-accent-foreground\"\n                              : \"hover:bg-accent/50\"\n                          }`}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          <div className={`p-2 rounded-md ${\n                            globalIndex === selectedIndex\n                              ? \"bg-primary text-primary-foreground\"\n                              : \"bg-muted\"\n                          }`}>\n                            <Icon className=\"h-4 w-4\" />\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <div className=\"font-medium\">{command.title}</div>\n                            <div className=\"text-sm text-muted-foreground truncate\">\n                              {command.description}\n                            </div>\n                          </div>\n                          {command.category === \"Social\" && (\n                            <ExternalLink className=\"h-3 w-3 text-muted-foreground\" />\n                          )}\n                        </motion.button>\n                      );\n                    })}\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div className=\"border-t p-3 text-xs text-muted-foreground flex items-center justify-between\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↑↓</Badge>\n              <span>Navigate</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">↵</Badge>\n              <span>Select</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Badge variant=\"outline\" className=\"text-xs px-1.5 py-0.5\">Esc</Badge>\n              <span>Close</span>\n            </div>\n          </div>\n          <div className=\"text-muted-foreground/60\">\n            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}\n          </div>\n        </div>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;AAxBA;;;;;;;;;AAyCO,SAAS,eAAe,EAAE,IAAI,EAAE,YAAY,EAAuB;IACxE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,WAAsB;QAC1B,aAAa;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,mMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAQ;aAAU;QACvC;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAO;gBAAc;aAAS;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,4MAAA,CAAA,YAAS;YACf,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAQ;gBAAa;aAAW;QACzD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,8MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAQ;gBAAY;gBAAW;aAAQ;QACpD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC;YAC1B,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAS;gBAAW;aAAO;QACnD;QACA,iBAAiB;QACjB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,QAAQ,IAAM,OAAO,IAAI,CAAC,kCAAkC;YAC5D,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAQ;gBAAgB;aAAc;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,QAAQ,IAAM,OAAO,IAAI,CAAC,uCAAuC;YACjE,UAAU;YACV,UAAU;gBAAC;gBAAY;gBAAgB;gBAAW;aAAS;QAC7D;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,wMAAA,CAAA,UAAO;YACb,QAAQ,IAAM,OAAO,IAAI,CAAC,mCAAmC;YAC7D,UAAU;YACV,UAAU;gBAAC;gBAAW;gBAAU;gBAAW;aAAW;QACxD;QACA,gBAAgB;QAChB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ,IAAM,OAAO,IAAI,CAAC,gCAAgC;YAC1D,UAAU;YACV,UAAU;gBAAC;gBAAS;gBAAW;gBAAW;aAAO;QACnD;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,sNAAA,CAAA,eAAY;YAClB,QAAQ,IAAM,OAAO,IAAI,CAAC,eAAe;YACzC,UAAU;YACV,UAAU;gBAAC;gBAAU;gBAAM;gBAAY;aAAO;QAChD;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAC;QACxC,MAAM,cAAc,OAAO,WAAW;QACtC,OACE,QAAQ,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC3C,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC,UAAY,QAAQ,QAAQ,CAAC;IAExD;IAEA,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAC,KAAK;QACpD,IAAI,CAAC,GAAG,CAAC,QAAQ,QAAQ,CAAC,EAAE;YAC1B,GAAG,CAAC,QAAQ,QAAQ,CAAC,GAAG,EAAE;QAC5B;QACA,GAAG,CAAC,QAAQ,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC3B,OAAO;IACT,GAAG,CAAC;IAEJ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iBAAiB;IACnB,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,MAAM;YAEX,IAAI,EAAE,GAAG,KAAK,aAAa;gBACzB,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,iBAAiB,MAAM,GAAG,IAAI,OAAO,IAAI;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;gBAC9B,EAAE,cAAc;gBAChB,iBAAiB,CAAC,OAChB,OAAO,IAAI,OAAO,IAAI,iBAAiB,MAAM,GAAG;YAEpD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;gBAC5B,EAAE,cAAc;gBAChB,IAAI,gBAAgB,CAAC,cAAc,EAAE;oBACnC,gBAAgB,CAAC,cAAc,CAAC,MAAM;oBACtC,aAAa;oBACb,UAAU;gBACZ;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAM;QAAe;QAAkB;KAAa;IAExD,MAAM,sBAAsB,CAAC;QAC3B,QAAQ,MAAM;QACd,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;sCAAU;;;;;;sCACjC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC,iIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;oCACV,SAAS;;;;;;;;;;;;;;;;;;8BAKf,8OAAC;oBAAI,WAAU;8BACZ,OAAO,IAAI,CAAC,iBAAiB,MAAM,KAAK,kBACvC,8OAAC;wBAAI,WAAU;;4BAAyC;4BAC9B;4BAAO;;;;;;6CAGjC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBACxD,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAEH,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4CACtB,MAAM,cAAc,iBAAiB,OAAO,CAAC;4CAC7C,MAAM,OAAO,QAAQ,IAAI;4CAEzB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,SAAS,IAAM,oBAAoB;gDACnC,WAAW,CAAC,2FAA2F,EACrG,gBAAgB,gBACZ,qCACA,sBACJ;gDACF,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,UAAU;oDAAE,OAAO;gDAAK;;kEAExB,8OAAC;wDAAI,WAAW,CAAC,eAAe,EAC9B,gBAAgB,gBACZ,uCACA,YACJ;kEACA,cAAA,8OAAC;4DAAK,WAAU;;;;;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC3C,8OAAC;gEAAI,WAAU;0EACZ,QAAQ,WAAW;;;;;;;;;;;;oDAGvB,QAAQ,QAAQ,KAAK,0BACpB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;+CAxBrB,QAAQ,EAAE;;;;;wCA4BrB;;;;;;;+BAvCM;;;;;;;;;;;;;;;8BA+ClB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAwB;;;;;;sDAC3D,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAGV,8OAAC;4BAAI,WAAU;;gCACZ,iBAAiB,MAAM;gCAAC;gCAAQ,iBAAiB,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAMnF", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/navigation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { useTheme } from \"next-themes\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport {\n  Moon,\n  Sun,\n  Menu,\n  X,\n  Home,\n  User,\n  Briefcase,\n  Mail,\n  FileText,\n  Search\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { CommandPalette } from \"@/components/command-palette\";\nimport { cn } from \"@/lib/utils\";\n\nconst navItems = [\n  { name: \"Home\", href: \"/\", icon: Home },\n  { name: \"About\", href: \"/about\", icon: User },\n  { name: \"Projects\", href: \"/projects\", icon: Briefcase },\n  { name: \"Blog\", href: \"/blog\", icon: FileText },\n  { name: \"Contact\", href: \"/contact\", icon: Mail },\n];\n\nexport function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [scrolled, setScrolled] = useState(false);\n  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.metaKey || e.ctrlKey) && e.key === \"k\") {\n        e.preventDefault();\n        setCommandPaletteOpen(true);\n      }\n    };\n\n    window.addEventListener(\"keydown\", handleKeyDown);\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\n  }, []);\n\n  const toggleTheme = () => {\n    setTheme(theme === \"dark\" ? \"light\" : \"dark\");\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-50 transition-all duration-300\",\n        scrolled\n          ? \"bg-background/80 backdrop-blur-md border-b border-border\"\n          : \"bg-transparent\"\n      )}\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      <nav className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex-shrink-0\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue\">\n              AK\n            </Link>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navItems.map((item) => (\n                <motion.div\n                  key={item.name}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <Link\n                    href={item.href}\n                    className=\"text-foreground/80 hover:text-foreground px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 hover:bg-accent\"\n                  >\n                    {item.name}\n                  </Link>\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Command Palette, Theme Toggle & Mobile Menu Button */}\n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => setCommandPaletteOpen(true)}\n              className=\"hidden md:flex items-center space-x-2 text-muted-foreground hover:text-foreground\"\n            >\n              <Search className=\"h-4 w-4\" />\n              <span className=\"text-sm\">Search</span>\n              <kbd className=\"pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100\">\n                <span className=\"text-xs\">⌘</span>K\n              </kbd>\n            </Button>\n\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={toggleTheme}\n              className=\"w-9 h-9\"\n            >\n              {theme === \"dark\" ? (\n                <Sun className=\"h-4 w-4\" />\n              ) : (\n                <Moon className=\"h-4 w-4\" />\n              )}\n            </Button>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"w-9 h-9\"\n              >\n                {isOpen ? (\n                  <X className=\"h-4 w-4\" />\n                ) : (\n                  <Menu className=\"h-4 w-4\" />\n                )}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isOpen && (\n            <motion.div\n              className=\"md:hidden\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-background/95 backdrop-blur-md rounded-lg mt-2 border border-border\">\n                {navItems.map((item) => {\n                  const Icon = item.icon;\n                  return (\n                    <motion.div\n                      key={item.name}\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Link\n                        href={item.href}\n                        className=\"text-foreground/80 hover:text-foreground flex items-center px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 hover:bg-accent\"\n                        onClick={() => setIsOpen(false)}\n                      >\n                        <Icon className=\"h-4 w-4 mr-3\" />\n                        {item.name}\n                      </Link>\n                    </motion.div>\n                  );\n                })}\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </nav>\n\n      <CommandPalette\n        open={commandPaletteOpen}\n        onOpenChange={setCommandPaletteOpen}\n      />\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AApBA;;;;;;;;;;AAsBA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtC;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM,kMAAA,CAAA,OAAI;IAAC;IAC5C;QAAE,MAAM;QAAY,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;IAAC;IACvD;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM,8MAAA,CAAA,WAAQ;IAAC;IAC9C;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM,kMAAA,CAAA,OAAI;IAAC;CACjD;AAEM,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,YAAY,OAAO,OAAO,GAAG;QAC/B;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,sBAAsB;YACxB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,WACI,6DACA;QAEN,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;0CAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAwC;;;;;;;;;;;0CAMnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CARP,KAAK,IAAI;;;;;;;;;;;;;;;0CAgBtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,sBAAsB;wCACrC,WAAU;;0DAEV,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAU;;;;;;oDAAQ;;;;;;;;;;;;;kDAItC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAET,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;iEAEf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAKpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAU;sDAET,uBACC,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ1B,8OAAC,yLAAA,CAAA,kBAAe;kCACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BACjC,SAAS;gCAAE,SAAS;gCAAG,QAAQ;4BAAO;4BACtC,MAAM;gCAAE,SAAS;gCAAG,QAAQ;4BAAE;4BAC9B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC;oCACb,MAAM,OAAO,KAAK,IAAI;oCACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,UAAU;;8DAEzB,8OAAC;oDAAK,WAAU;;;;;;gDACf,KAAK,IAAI;;;;;;;uCAVP,KAAK,IAAI;;;;;gCAcpB;;;;;;;;;;;;;;;;;;;;;;0BAOV,8OAAC,wIAAA,CAAA,iBAAc;gBACb,MAAM;gBACN,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 1236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { \n  Github, \n  Linkedin, \n  Twitter, \n  Mail, \n  Heart, \n  ArrowUp,\n  Code,\n  Coffee\n} from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\nconst footerLinks = {\n  navigation: [\n    { name: \"Home\", href: \"/\" },\n    { name: \"About\", href: \"/about\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n  services: [\n    { name: \"Full Stack Development\", href: \"/#services\" },\n    { name: \"UI/UX Design\", href: \"/#services\" },\n    { name: \"Mobile Development\", href: \"/#services\" },\n    { name: \"Consulting\", href: \"/#services\" },\n  ],\n  resources: [\n    { name: \"Blog\", href: \"/blog\" },\n    { name: \"Projects\", href: \"/projects\" },\n    { name: \"Tech Stack\", href: \"/#tech-stack\" },\n    { name: \"Contact\", href: \"/contact\" },\n  ],\n};\n\nconst socialLinks = [\n  {\n    name: \"GitHub\",\n    href: \"https://github.com/ashishkamat\",\n    icon: Github,\n    color: \"hover:text-gray-600 dark:hover:text-gray-300\",\n  },\n  {\n    name: \"LinkedIn\",\n    href: \"https://linkedin.com/in/ashishkamat\",\n    icon: Linkedin,\n    color: \"hover:text-blue-600\",\n  },\n  {\n    name: \"Twitter\",\n    href: \"https://twitter.com/ashishkamat\",\n    icon: Twitter,\n    color: \"hover:text-blue-500\",\n  },\n  {\n    name: \"Email\",\n    href: \"mailto:<EMAIL>\",\n    icon: Mail,\n    color: \"hover:text-green-600\",\n  },\n];\n\nexport function Footer() {\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  return (\n    <footer className=\"bg-background border-t border-border\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-12 lg:py-16\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-2\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5 }}\n                viewport={{ once: true }}\n              >\n                <Link href=\"/\" className=\"text-2xl font-bold gradient-text-blue mb-4 inline-block\">\n                  Ashish Kamat\n                </Link>\n                <p className=\"text-muted-foreground mb-6 max-w-md\">\n                  Full Stack Developer & UI/UX Designer passionate about creating \n                  innovative digital experiences that make a difference.\n                </p>\n                \n                {/* Social Links */}\n                <div className=\"flex space-x-4\">\n                  {socialLinks.map((social, index) => {\n                    const Icon = social.icon;\n                    return (\n                      <motion.a\n                        key={social.name}\n                        href={social.href}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className={`p-2 rounded-lg bg-muted hover:bg-accent transition-all duration-300 ${social.color}`}\n                        whileHover={{ scale: 1.1 }}\n                        whileTap={{ scale: 0.9 }}\n                        initial={{ opacity: 0, scale: 0 }}\n                        whileInView={{ opacity: 1, scale: 1 }}\n                        transition={{ duration: 0.3, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                      >\n                        <Icon className=\"h-5 w-5\" />\n                      </motion.a>\n                    );\n                  })}\n                </div>\n              </motion.div>\n            </div>\n\n            {/* Navigation Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Navigation</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.navigation.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Services Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Services</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.services.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h3 className=\"font-semibold text-foreground mb-4\">Resources</h3>\n              <ul className=\"space-y-3\">\n                {footerLinks.resources.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-muted-foreground hover:text-foreground transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          className=\"py-6 border-t border-border\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.4 }}\n          viewport={{ once: true }}\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            {/* Copyright */}\n            <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n              <span>© 2024 Ashish Kamat. Made with</span>\n              <Heart className=\"h-4 w-4 text-red-500 animate-pulse\" />\n              <span>and</span>\n              <Coffee className=\"h-4 w-4 text-amber-600\" />\n              <span>in Kathmandu</span>\n            </div>\n\n            {/* Tech Stack & Back to Top */}\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"flex items-center space-x-2 text-sm text-muted-foreground\">\n                <Code className=\"h-4 w-4\" />\n                <span>Built with Next.js & Tailwind CSS</span>\n              </div>\n              \n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                onClick={scrollToTop}\n                className=\"rounded-full hover:scale-110 transition-transform duration-200\"\n              >\n                <ArrowUp className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Fun Easter Egg */}\n        <motion.div\n          className=\"text-center py-4 border-t border-border/50\"\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.6 }}\n          viewport={{ once: true }}\n        >\n          <p className=\"text-xs text-muted-foreground/70\">\n            🚀 This website is powered by{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% coffee\n            </span>{\" \"}\n            and{\" \"}\n            <span className=\"font-mono bg-muted px-1 py-0.5 rounded text-xs\">\n              {Math.floor(Math.random() * 100)}% passion\n            </span>\n          </p>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAgBA,MAAM,cAAc;IAClB,YAAY;QACV;YAAE,MAAM;YAAQ,MAAM;QAAI;QAC1B;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IACD,UAAU;QACR;YAAE,MAAM;YAA0B,MAAM;QAAa;QACrD;YAAE,MAAM;YAAgB,MAAM;QAAa;QAC3C;YAAE,MAAM;YAAsB,MAAM;QAAa;QACjD;YAAE,MAAM;YAAc,MAAM;QAAa;KAC1C;IACD,WAAW;QACT;YAAE,MAAM;YAAQ,MAAM;QAAQ;QAC9B;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAe;QAC3C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;IACT;CACD;AAEM,SAAS;IACd,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,UAAU;wCAAE,MAAM;oCAAK;;sDAEvB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA0D;;;;;;sDAGnF,8OAAC;4CAAE,WAAU;sDAAsC;;;;;;sDAMnD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,oEAAoE,EAAE,OAAO,KAAK,EAAE;oDAChG,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,aAAa;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDACpC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,8OAAC;wDAAK,WAAU;;;;;;mDAZX,OAAO,IAAI;;;;;4CAetB;;;;;;;;;;;;;;;;;0CAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;0CAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,8OAAC;wCAAG,WAAU;kDACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAe5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;kDAAK;;;;;;kDACN,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;0CAIR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAGR,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC;wBAAE,WAAU;;4BAAmC;4BAChB;0CAC9B,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;4BAC3B;4BAAI;4BACR;0CACJ,8OAAC;gCAAK,WAAU;;oCACb,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;oCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-hero.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport { Search, Rss, BookOpen, TrendingUp } from \"lucide-react\";\nimport { Input } from \"@/components/ui/input\";\nimport { Button } from \"@/components/ui/button\";\n\nexport function BlogHero() {\n  return (\n    <section className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <div className=\"space-y-4\">\n              <motion.h1\n                className=\"text-4xl sm:text-5xl lg:text-6xl font-bold\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                My <span className=\"gradient-text-blue\">Blog</span>\n              </motion.h1>\n              \n              <motion.p\n                className=\"text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                Thoughts, tutorials, and insights on web development, React, TypeScript, \n                and the ever-evolving world of modern frontend technologies.\n              </motion.p>\n            </div>\n\n            {/* Search Bar */}\n            <motion.div\n              className=\"max-w-md mx-auto\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n            >\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n                <Input\n                  placeholder=\"Search articles...\"\n                  className=\"pl-10 pr-4 py-3 text-base\"\n                />\n              </div>\n            </motion.div>\n\n            {/* Blog Stats */}\n            <motion.div\n              className=\"grid grid-cols-3 gap-8 max-w-md mx-auto\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n            >\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mx-auto mb-2\">\n                  <BookOpen className=\"h-6 w-6 text-blue-500\" />\n                </div>\n                <div className=\"text-2xl font-bold\">25+</div>\n                <div className=\"text-sm text-muted-foreground\">Articles</div>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center mx-auto mb-2\">\n                  <TrendingUp className=\"h-6 w-6 text-green-500\" />\n                </div>\n                <div className=\"text-2xl font-bold\">10K+</div>\n                <div className=\"text-sm text-muted-foreground\">Readers</div>\n              </div>\n\n              <div className=\"text-center\">\n                <div className=\"w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center mx-auto mb-2\">\n                  <Rss className=\"h-6 w-6 text-purple-500\" />\n                </div>\n                <div className=\"text-2xl font-bold\">500+</div>\n                <div className=\"text-sm text-muted-foreground\">Subscribers</div>\n              </div>\n            </motion.div>\n\n            {/* RSS Subscribe */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6 }}\n            >\n              <Button variant=\"outline\" className=\"group\">\n                <Rss className=\"mr-2 h-4 w-4 group-hover:animate-pulse\" />\n                Subscribe to RSS\n              </Button>\n            </motion.div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOO,SAAS;IACd,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;;wCAC1B;sDACI,8OAAC;4CAAK,WAAU;sDAAqB;;;;;;;;;;;;8CAG1C,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAC1B;;;;;;;;;;;;sCAOH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAKnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;sCAEzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E", "debugId": null}}, {"offset": {"line": 2157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/lib/api.ts"], "sourcesContent": ["// API client for CMS integration\nconst CMS_BASE_URL = process.env.NEXT_PUBLIC_CMS_URL || 'http://localhost:3000'\n\nexport interface Project {\n  id: string\n  title: string\n  description: string\n  longDescription?: string\n  image?: string\n  category: string\n  technologies: string[]\n  liveUrl?: string\n  githubUrl?: string\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  image?: string\n  category: string\n  tags: string[]\n  published: boolean\n  featured: boolean\n  readTime?: number\n  views: number\n  createdAt: string\n  updatedAt: string\n  publishedAt?: string\n}\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  icon: string\n  color: string\n  bgColor: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface TechStack {\n  id: string\n  name: string\n  logo: string\n  color: string\n  category: string\n  order: number\n  published: boolean\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  role: string\n  company: string\n  content: string\n  avatar?: string\n  rating: number\n  featured: boolean\n  published: boolean\n  order: number\n  createdAt: string\n  updatedAt: string\n}\n\n// API functions\nexport const api = {\n  // Projects\n  getProjects: async (): Promise<Project[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch projects')\n    }\n    return response.json()\n  },\n\n  getProject: async (id: string): Promise<Project> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/projects/${id}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch project')\n    }\n    return response.json()\n  },\n\n  // Blog Posts\n  getBlogPosts: async (): Promise<BlogPost[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog posts')\n    }\n    return response.json()\n  },\n\n  getBlogPost: async (slug: string): Promise<BlogPost> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/blog/${slug}`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch blog post')\n    }\n    return response.json()\n  },\n\n  // Services\n  getServices: async (): Promise<Service[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/services`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch services')\n    }\n    return response.json()\n  },\n\n  // Tech Stack\n  getTechStack: async (): Promise<TechStack[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/tech-stack`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch tech stack')\n    }\n    return response.json()\n  },\n\n  // Testimonials\n  getTestimonials: async (): Promise<Testimonial[]> => {\n    const response = await fetch(`${CMS_BASE_URL}/api/testimonials`)\n    if (!response.ok) {\n      throw new Error('Failed to fetch testimonials')\n    }\n    return response.json()\n  },\n}\n\n// Helper functions\nexport const getPublishedProjects = (projects: Project[]) => \n  projects.filter(project => project.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedProjects = (projects: Project[]) => \n  projects.filter(project => project.published && project.featured).sort((a, b) => a.order - b.order)\n\nexport const getProjectsByCategory = (projects: Project[], category: string) => \n  category === 'All' \n    ? getPublishedProjects(projects)\n    : projects.filter(project => project.published && project.category === category).sort((a, b) => a.order - b.order)\n\nexport const getPublishedServices = (services: Service[]) => \n  services.filter(service => service.published).sort((a, b) => a.order - b.order)\n\nexport const getTechStackByCategory = (techStack: TechStack[]) => {\n  const published = techStack.filter(tech => tech.published)\n  return published.reduce((acc, tech) => {\n    if (!acc[tech.category]) {\n      acc[tech.category] = []\n    }\n    acc[tech.category].push(tech)\n    return acc\n  }, {} as Record<string, TechStack[]>)\n}\n\nexport const getPublishedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published).sort((a, b) => a.order - b.order)\n\nexport const getFeaturedTestimonials = (testimonials: Testimonial[]) => \n  testimonials.filter(testimonial => testimonial.published && testimonial.featured).sort((a, b) => a.order - b.order)\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;AACjC,MAAM,eAAe,6DAAmC;AA+EjD,MAAM,MAAM;IACjB,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,cAAc,EAAE,IAAI;QACjE,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,SAAS,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,UAAU,EAAE,MAAM;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,WAAW;IACX,aAAa;QACX,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,aAAa,CAAC;QAC3D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc;QACZ,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC;QAC7D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,iBAAiB;QACf,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,iBAAiB,CAAC;QAC/D,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,sBAAsB,CAAC,WAClC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE7F,MAAM,wBAAwB,CAAC,UAAqB,WACzD,aAAa,QACT,qBAAqB,YACrB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,IAAI,QAAQ,QAAQ,KAAK,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAE9G,MAAM,uBAAuB,CAAC,WACnC,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAEzE,MAAM,yBAAyB,CAAC;IACrC,MAAM,YAAY,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;IACzD,OAAO,UAAU,MAAM,CAAC,CAAC,KAAK;QAC5B,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE;YACvB,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;QACzB;QACA,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,MAAM,2BAA2B,CAAC,eACvC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AAErF,MAAM,0BAA0B,CAAC,eACtC,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,SAAS,IAAI,YAAY,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK", "debugId": null}}, {"offset": {"line": 2344, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-grid.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Calendar, Clock, ArrowRight, Tag, Eye, Loader2 } from \"lucide-react\";\nimport { <PERSON>, CardContent, CardHeader } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport Image from \"next/image\";\nimport { api, type BlogPost } from \"@/lib/api\";\n\nexport function BlogGrid() {\n  const [selectedCategory, setSelectedCategory] = useState(\"All\");\n  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  // Fetch blog posts from CMS\n  useEffect(() => {\n    const fetchBlogPosts = async () => {\n      try {\n        setIsLoading(true);\n        const data = await api.getBlogPosts();\n        // Filter published posts only\n        const publishedPosts = data.filter(post => post.published);\n        setBlogPosts(publishedPosts);\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchBlogPosts();\n  }, []);\n\n  // Memoized computed values\n  const categories = React.useMemo((): string[] => {\n    if (!blogPosts.length) return [\"All\"];\n    const uniqueCategories = Array.from(new Set(blogPosts.map((p: any) => p.category as string)));\n    return [\"All\", ...uniqueCategories.sort()];\n  }, [blogPosts]);\n\n  const filteredPosts = React.useMemo(() => {\n    if (selectedCategory === \"All\") return blogPosts;\n    return blogPosts.filter(post => post.category === selectedCategory);\n  }, [blogPosts, selectedCategory]);\n\n  // Loading state\n  if (isLoading) {\n    return (\n      <section className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"gradient-text\">Latest Articles</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Loading blog posts...\n            </p>\n          </div>\n          <div className=\"flex justify-center\">\n            <Loader2 className=\"h-8 w-8 animate-spin\" />\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <section className=\"py-20 bg-background\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n              <span className=\"gradient-text\">Latest Articles</span>\n            </h2>\n            <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n              Unable to load blog posts. Please try again later.\n            </p>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  return (\n    <section ref={ref} className=\"py-20 bg-background\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-16\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold mb-6\">\n            <span className=\"gradient-text\">Latest Articles</span>\n          </h2>\n          <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n            Insights, tutorials, and thoughts on modern web development, React, TypeScript, and more.\n          </p>\n        </motion.div>\n\n        {/* Category Filter */}\n        <motion.div\n          className=\"flex flex-wrap justify-center gap-2 mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8, delay: 0.2 }}\n        >\n          {categories.map((category: string) => (\n            <Button\n              key={category}\n              variant={selectedCategory === category ? \"default\" : \"outline\"}\n              size=\"sm\"\n              onClick={() => setSelectedCategory(category)}\n              className=\"transition-all duration-300\"\n            >\n              {category}\n            </Button>\n          ))}\n        </motion.div>\n\n        {/* Blog Posts Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredPosts.map((post, index) => (\n            <motion.div\n              key={post.id}\n              initial={{ opacity: 0, y: 50 }}\n              animate={inView ? { opacity: 1, y: 0 } : {}}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n            >\n              <Card className=\"h-full hover-lift group cursor-pointer border-border/50 hover:border-border transition-all duration-300\">\n                <Link href={`/blog/${post.slug}`}>\n                  <div className=\"relative h-48 overflow-hidden rounded-t-lg\">\n                    {post.image ? (\n                      <Image\n                        src={post.image}\n                        alt={post.title}\n                        fill\n                        className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                      />\n                    ) : (\n                      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 flex items-center justify-center\">\n                        <div className=\"text-4xl opacity-20\">📝</div>\n                      </div>\n                    )}\n                    <div className=\"absolute top-3 right-3\">\n                      <Badge variant=\"secondary\" className=\"text-xs bg-background/80 backdrop-blur-sm\">\n                        {post.category}\n                      </Badge>\n                    </div>\n                  </div>\n                  \n                  <CardHeader className=\"pb-3\">\n                    <h3 className=\"text-xl font-bold group-hover:text-primary transition-colors duration-300 line-clamp-2\">\n                      {post.title}\n                    </h3>\n                  </CardHeader>\n                  \n                  <CardContent className=\"space-y-4\">\n                    <p className=\"text-muted-foreground text-sm line-clamp-3\">\n                      {post.excerpt}\n                    </p>\n                    \n                    <div className=\"flex flex-wrap gap-1\">\n                      {post.tags.slice(0, 3).map((tag, tagIndex) => (\n                        <Badge key={tagIndex} variant=\"outline\" className=\"text-xs\">\n                          {tag}\n                        </Badge>\n                      ))}\n                      {post.tags.length > 3 && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          +{post.tags.length - 3}\n                        </Badge>\n                      )}\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between text-xs text-muted-foreground pt-2\">\n                      <div className=\"flex items-center space-x-4\">\n                        <div className=\"flex items-center\">\n                          <Calendar className=\"mr-1 h-3 w-3\" />\n                          {new Date(post.publishedAt || post.createdAt).toLocaleDateString()}\n                        </div>\n                        <div className=\"flex items-center\">\n                          <Clock className=\"mr-1 h-3 w-3\" />\n                          {post.readTime || '5 min read'}\n                        </div>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Eye className=\"mr-1 h-3 w-3\" />\n                        {post.views}\n                      </div>\n                    </div>\n                    \n                    <Button variant=\"ghost\" className=\"w-full group-hover:bg-accent transition-colors duration-300\">\n                      Read More\n                      <ArrowRight className=\"ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                    </Button>\n                  </CardContent>\n                </Link>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAaO,SAAS;IACd,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,aAAa;gBACb,MAAM,OAAO,MAAM,iHAAA,CAAA,MAAG,CAAC,YAAY;gBACnC,8BAA8B;gBAC9B,MAAM,iBAAiB,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS;gBACzD,aAAa;gBACb,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,aAAa,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,UAAU,MAAM,EAAE,OAAO;YAAC;SAAM;QACrC,MAAM,mBAAmB,MAAM,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC,IAAW,EAAE,QAAQ;QAChF,OAAO;YAAC;eAAU,iBAAiB,IAAI;SAAG;IAC5C,GAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAClC,IAAI,qBAAqB,OAAO,OAAO;QACvC,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IACpD,GAAG;QAAC;QAAW;KAAiB;IAEhC,gBAAgB;IAChB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACZ,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;;;;;;;kCAIjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;IAK7B;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC;YAAQ,WAAU;sBACjB,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;;;;;;;;;;;IAOzE;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCACZ,cAAA,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;sCAElC,8OAAC;4BAAE,WAAU;sCAAkD;;;;;;;;;;;;8BAMjE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI,CAAC;oBAC1C,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAEvC,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,kIAAA,CAAA,SAAM;4BAEL,SAAS,qBAAqB,WAAW,YAAY;4BACrD,MAAK;4BACL,SAAS,IAAM,oBAAoB;4BACnC,WAAU;sCAET;2BANI;;;;;;;;;;8BAYX,8OAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,KAAK,iBACT,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,KAAK;oDACf,IAAI;oDACJ,WAAU;;;;;yEAGZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAsB;;;;;;;;;;;8DAGzC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;kEAClC,KAAK,QAAQ;;;;;;;;;;;;;;;;;sDAKpB,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC;gDAAG,WAAU;0DACX,KAAK,KAAK;;;;;;;;;;;sDAIf,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;8DAGf,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,yBAC/B,8OAAC,iIAAA,CAAA,QAAK;gEAAgB,SAAQ;gEAAU,WAAU;0EAC/C;+DADS;;;;;wDAIb,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAU;gEACzC,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;8DAK3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;wEACnB,IAAI,KAAK,KAAK,WAAW,IAAI,KAAK,SAAS,EAAE,kBAAkB;;;;;;;8EAElE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,KAAK,QAAQ,IAAI;;;;;;;;;;;;;sEAGtB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,KAAK,KAAK;;;;;;;;;;;;;8DAIf,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,WAAU;;wDAA8D;sEAE9F,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAtEzB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;AAiF1B", "debugId": null}}, {"offset": {"line": 2871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/portfolio/ashish-portfolio/src/components/blog/blog-newsletter.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { motion } from \"framer-motion\";\nimport { useInView } from \"react-intersection-observer\";\nimport { Mail, Send, CheckCircle } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { toast } from \"sonner\";\n\nexport function BlogNewsletter() {\n  const [email, setEmail] = useState(\"\");\n  const [isSubscribed, setIsSubscribed] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1,\n  });\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n\n    setIsLoading(true);\n    \n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      setIsSubscribed(true);\n      toast.success(\"Successfully subscribed to the newsletter!\");\n      setEmail(\"\");\n    } catch (error) {\n      toast.error(\"Failed to subscribe. Please try again.\");\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <section ref={ref} className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"max-w-2xl mx-auto\"\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : {}}\n          transition={{ duration: 0.8 }}\n        >\n          <Card className=\"border-border/50 bg-gradient-to-br from-background to-muted/30\">\n            <CardContent className=\"p-8 text-center\">\n              <motion.div\n                className=\"space-y-6\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={inView ? { opacity: 1, y: 0 } : {}}\n                transition={{ duration: 0.6, delay: 0.2 }}\n              >\n                <div className=\"w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mx-auto\">\n                  <Mail className=\"h-8 w-8 text-primary\" />\n                </div>\n                \n                <div className=\"space-y-2\">\n                  <h3 className=\"text-2xl font-bold\">Stay Updated</h3>\n                  <p className=\"text-muted-foreground\">\n                    Get the latest articles, tutorials, and insights delivered straight to your inbox. \n                    No spam, unsubscribe at any time.\n                  </p>\n                </div>\n\n                {!isSubscribed ? (\n                  <form onSubmit={handleSubmit} className=\"space-y-4\">\n                    <div className=\"flex flex-col sm:flex-row gap-3\">\n                      <Input\n                        type=\"email\"\n                        placeholder=\"Enter your email address\"\n                        value={email}\n                        onChange={(e) => setEmail(e.target.value)}\n                        className=\"flex-1\"\n                        required\n                      />\n                      <Button \n                        type=\"submit\" \n                        disabled={isLoading}\n                        className=\"group\"\n                      >\n                        {isLoading ? (\n                          <>\n                            <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n                            Subscribing...\n                          </>\n                        ) : (\n                          <>\n                            <Send className=\"mr-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300\" />\n                            Subscribe\n                          </>\n                        )}\n                      </Button>\n                    </div>\n                    <p className=\"text-xs text-muted-foreground\">\n                      Join 500+ developers who get weekly updates on web development trends and tutorials.\n                    </p>\n                  </form>\n                ) : (\n                  <motion.div\n                    className=\"space-y-4\"\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5 }}\n                  >\n                    <div className=\"w-16 h-16 rounded-full bg-green-500/10 flex items-center justify-center mx-auto\">\n                      <CheckCircle className=\"h-8 w-8 text-green-500\" />\n                    </div>\n                    <div>\n                      <h4 className=\"text-xl font-semibold text-green-600 dark:text-green-400\">\n                        Successfully Subscribed!\n                      </h4>\n                      <p className=\"text-muted-foreground mt-2\">\n                        Thank you for subscribing! You'll receive a confirmation email shortly.\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n\n                <div className=\"grid grid-cols-3 gap-6 pt-6 border-t border-border/50\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-primary\">500+</div>\n                    <div className=\"text-sm text-muted-foreground\">Subscribers</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-primary\">25+</div>\n                    <div className=\"text-sm text-muted-foreground\">Articles</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-primary\">Weekly</div>\n                    <div className=\"text-sm text-muted-foreground\">Updates</div>\n                  </div>\n                </div>\n              </motion.div>\n            </CardContent>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWO,SAAS;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,OAAO;QAEZ,aAAa;QAEb,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,gBAAgB;YAChB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,SAAS;QACX,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE,IAAI,CAAC;gBAC1C,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAExC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;gCAMtC,CAAC,6BACA,8OAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,QAAQ;;;;;;8DAEV,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,0BACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAAmE;;qFAIpF;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAA6E;;;;;;;;;;;;;;sDAMrG,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;yDAK/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;oCAAI;;sDAE5B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA2D;;;;;;8DAGzE,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;;;;;8CAOhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE", "debugId": null}}]}